<?php
/**
 * Created by PhpStorm.
 * User: pang<PERSON><PERSON><PERSON>@zuoyebang.com
 * Date: 2019/4/3
 * Time: 2:25 PM
 * 方舟中台化配置
 * 方舟任务分为两层，一层是通用任务如：电话家访率  一层是明细任务如：N1学员电话家访率
 */
class AssistantDesk_ArkConfig {
    // 二维码组件[新 newQrcodeTool]
    /**
     * 建议使用新的组件功能，旧的组件因为入参写到了一起，不容易区分逻辑和做字段扩展
     */
    const QRCODE_TOOL_MRYL = 1001; //  每日一练 二维码组件
    const QRCODE_TOOL_RJYL = 1002; //  日积月累 二维码组件

    // 此配置 新旧二维码工具都在使用，分别对应后端两个获取二维码链接的接口，因为牵涉到小程序的逻辑，
    // 后续会找时间梳理一下参数，把接口统一逻辑，新增二维码请尽量写到新的逻辑里面
    //【注意接口入参和返回值 有参数大小写的问题，有一点区别】
    public static $qrCodeMap = [
        Wx_Config::TYPE_PREVIEW  => [
            'qrCodeButtonName' => "扫描分享",
            'qrCodeName'       => "预习小程序",
            'qrCodeType'       => Wx_Config::TYPE_PREVIEW,
            'qrCodeChannel'    => Wx_Config::PREVIEW_QR_CHANNEL_1,
        ],
        Wx_Config::TYPE_HOMEWORK => [
            'qrCodeButtonName' => "扫描分享",
            'qrCodeName'       => "巩固练习小程序",
            'qrCodeType'       => Wx_Config::TYPE_HOMEWORK,
            'qrCodeChannel'    => Wx_Config::HOMEWORK_QR_CHANNEL_3,
        ],
        Wx_Config::TYPE_EXPLAIN  => [
            'qrCodeButtonName' => "复制分享",
            'qrCodeType'       => Wx_Config::TYPE_EXPLAIN,
            'qrCodeChannel'    => Wx_Config::EXPLAIN_QR_CHANNEL_4,
        ],
        self::QRCODE_TOOL_MRYL  => [
            'qrCodeButtonName' => "复制分享",
            'qrCodeType'       => self::QRCODE_TOOL_MRYL,
        ],
        self::QRCODE_TOOL_RJYL  => [
            'qrCodeButtonName' => "复制分享",
            'qrCodeType'       => self::QRCODE_TOOL_RJYL,
        ]
    ];

    // 日期选择工具
    const TOOLS_DATE_RADIO_DAY = 1001;

    //计算指标时需要过滤的学生类型
    public static $filterUserType = ['T'];

    //章节计算逻辑常量
    const LESSON_LIST_TYPE_ALL      = 1;
    const LESSON_LIST_TYPE_PREVIEW  = 2;
    const LESSON_LIST_TYPE_HOMEWORK = 3;
    const LESSON_LIST_TYPE_INCLASS_TEST = 4;
    const LESSON_LIST_TYPE_STAGE_TEST = 5;

    //章节逻辑类型对应测试类型
    public static $lessonListExamTypeMap = [
        self::LESSON_LIST_TYPE_HOMEWORK      => Api_Exam::BIND_TYPE_HOMEWORK,
        self::LESSON_LIST_TYPE_INCLASS_TEST  => Api_Exam::BIND_TYPE_TEST_IN_CLASS,
        self::LESSON_LIST_TYPE_STAGE_TEST    => Api_Exam::BIND_TYPE_STAGE,
    ];

    /****************** 按钮类常量 start *********************/
    // 预习点评
    const BUTTON_TYPE_7 = 7;
    // 预习解析
    const BUTTON_TYPE_8 = 8;

    //按钮 - 题目解析
    const BUTTON_TYPE_ANALYSE_CHECK_TEST    = 9;
    const BUTTON_TYPE_ANALYSE_PREVIEW       = 10;
    const BUTTON_TYPE_ANALYSE_IN_CLASS_TEST = 11;
    const BUTTON_TYPE_ANALYSE_HOMEWORK      = 12;
    const BUTTON_TYPE_ANALYSE_STAGE_TEST    = 13;

    //按钮 - 题目讲解
    const BUTTON_TYPE_EXPOUND_CHECK_TEST    = 14;
    const BUTTON_TYPE_EXPOUND_PREVIEW       = 15;
    const BUTTON_TYPE_EXPOUND_IN_CLASS_TEST = 16;
    const BUTTON_TYPE_EXPOUND_HOMEWORK      = 17;
    const BUTTON_TYPE_EXPOUND_STAGE_TEST    = 18;

    // 按钮 - 催提交巩固练习
    const BUTTON_TYPE_SUBMIT_HOMEWORK         = 19;
    // 按钮 - 催订正巩固练习
    const BUTTON_TYPE_REVISE_HOMEWORK         = 20;
    // 催浣熊课前练习/能力挑战
    const BUTTON_TYPE_HX_PRE_CLASS_PRACTICE   = 21;
    const BUTTON_TYPE_HX_POWER_CHALLENGE      = 22;
    const BUTTON_TYPE_ATTEND_OVERVIEW         = 23;
    // 催家访问卷
    const BUTTON_TYPE_INTERVIEW_LABEL         = 24;
    // 催满意度问卷
    const BUTTON_TYPE_SATISFACTION_QUE        = 25;
    // 催口述题
    const BUTTON_TYPE_SUBMIT_ORAL_QUESTION    = 26;
    // 口述题反馈
    const BUTTON_TYPE_ORAL_QUESTION_FEEDBACK  = 27;
    // 批改结果反馈
    const BUTTON_TYPE_CORRECT_FEEDBACK        = 28;
    // 催月考
    const BUTTON_TYPE_SUBMIT_MONTHLY_EXAM     = 29;
    // 催打卡
    const BUTTON_TYPE_DA_KA                   = 30;
    // 创建打卡班级
    const BUTTON__TYPE_CREATE_KS_CLASS      = 31;
    // 同步打卡班级
    const BUTTON__TYPE_SYNC_KS_CLASS        = 32;
    // 作文报告反馈
    const BUTTON_TYPE_COMPOSITION_REPORT      = 33;
    // 优秀习作
    const BUTTON_TYPE_EXCELLENT_HOMEWORK      = 34;
    // 日积月累
    const BUTTON_TYPE_RJYL                    = 35;
    // 单元正确率看板
    const BUTTON_TYPE_UNIT_RATIO_BOARD      = 36;
    // 错题本-解析版
    const BUTTON_TYPE_CUOTIBEN_JIEXI          = 37;
    // 错题本-作答版
    const BUTTON_TYPE_CUOTIBEN_ZUODA          = 38;
    // 批量定向批改
    const BUTTON_TYPE_DX_CORRECT            = 39;
    // 取消定向批改
    const BUTTON_TYPE_CANCEL_DX_CORRECT     = 40;
    // 每日一练 查看题目预览按钮
    const BUTTON_TYPE_TOPIC_VIEW            = 41;
    // 每日一练 查看题目正确率看板
    const BUTTON_TYPE_TOPIC_RIGHT_RATE      = 42;
    // 每日一练 催每日一练
    const BUTTON_TYPE_REMIND_MRYL           = 43;
    // 催加微
    const BUTTON_TYPE_ADD_WECHAT            = 44;
    // 催入群
    const BUTTON_TYPE_JOIN_GROUP            = 45;
    //同步通讯录按钮
    const BUTTON_TYPE_SYNC_ADDRESS_BOOK     = 46;

    //  催个微好友加企微
    const BUTTON_TYPE_ADD_FRIENDS_QW        = 47;

    // 催下一学季老师加微
    const BUTTON_TYPE_ADD_NEW_TEACHER       = 48;

    // 小英月考报告
    const BUTTON_TYPE_MONTHLYEXAM_REPORT    = 49;

    // 导出excle按钮
    const BUTTON_TYPE_EXPORT_EXCLE          = 50;

    // 群发高光时刻
    const BUTTON_TYPE_HIGHLIGHT = 51;

    //群发转介绍海报
    const BUTTON_TYPE_INTRODUCEPOST = 52;

    const BUTTON_TYPE_ATTEND_OVERVIEW_PLAYBACK         = 53;

    const BUTTON_TYPE_RECRUITPOST = 55;

    const BUTTON_TYPE_STAGE_SUBMIT = 56;
    const BUTTON_TYPE_STAGE_RESULT_FEEDBACK = 57;

    const BUTTON_TYPE_LPC_ADD_SCORE          = 60; // 加学分工具
    const BUTTON_TYPE_LPC_FOLLOW             = 61; // 主讲关注学员
    const BUTTON_TYPE_LPC_DOWN_CLASS_NOTE    = 62; // 下载课堂笔记
    const BUTTON_TYPE_LPC_COPY_PLAYBACK_LINK = 63; // 复制回放链接
    const BUTTON_TYPE_LPC_REVIEW_IMAGE       = 64; // 预览知识打点图

    const BUTTON_TYPE_LPC_SMS_ADD_WX       = 65;//发送用户加微短信 65
    const BUTTON_TYPE_LPC_SMS_ADD_WX_APPLY = 66;//发送加用户微信申请 66
    const BUTTON_TYPE_LPC_SMS_ADD_WX_CARD  = 67;//发送用户名片给我 67
    const BUTTON_TYPE_LPC_SMS_ATTEND       = 68;//催到课短信 68
    const BUTTON_TYPE_GUANJIA_EXPORT_ANALYZE = 70; // 管家导出诊断结果
    const BUTTON_TYPE_RSYNC_CONTACT          = 72; // lpc 同步通讯录
    const BUTTON_TYPE_LPC_SMS_AI_ATTED       = 76; // AI课催到课
    const BUTTON_TYPE_FN_DEMAND_SURVEY       = 1500; // 蜂鸟挖需问卷

    // 按钮 - 催提交相似题
    const BUTTON_TYPE_SUBMIT_HOMEWORK_LIKE    = 73;
    // 按钮 - 催订正相似题相似题
    const BUTTON_TYPE_REVISE_HOMEWORK_LIKE    = 74;
    // 相似题批改结果反馈
    const BUTTON_TYPE_CORRECT_FEEDBACK_LIKE   = 75;

    // 按钮 - 快速拉群
    const SEND_TYPE_QW_ADD_GROUP              = 80;

    // 按钮 - 录入转介绍新用户
    const BUTTON_TYPE_INTRO_BINDING_NEW       = 81;
    // 按钮 - 生成老用户转介绍海报
    const BUTTON_TYPE_INTRO_POSTER            = 82;
    // 按钮 - 批改结果反馈（小鹿）
    const BUTTON_TYPE_CORRECT_FEEDBACK_DEER   = 83;

    const BUTTON_TYPE_SEND_EXERCISE_NOTE_TASK  = 90;
    const BUTTON_TYPE_SEND_EXERCISE_NOTE_CLEAN_REPORT  = 91;
    const BUTTON_TYPE_CLASS_MISTAKE_TI  = 92;


    const BUTTON_TYPE_PREVIEW_LPC           = 1001;  // lpc 催预习
    const BUTTON_TYPE_ATTEND_LPC            = 1003;  // lpc 催到课
    const BUTTON_TYPE_PLAYBACK_LPC          = 1004;  // lpc 催回放
    const BUTTON_TYPE_NOTES_LPC             = 1005;  // lpc 群发课程笔记
    const BUTTON_TYPE_BOTTOM_TEST_REPORT    = 1007;  // lpc 群发摸底测报告
    const BUTTON_TYPE_BOTTOM_TEST_REPORT_FD = 1071;
    const BUTTON_TYPE_BOTTOM_TEST           = 1008;  // lpc 催完成 摸底测
    const BUTTON_TYPE_PERIOD_EXAM           = 1009;  // lpc 催完成阶段测
    const BUTTON_TYPE_EXERCISE_REPORT       = 1012;  // lpc 群发巩固练习报告
    const BUTTON_TYPE_SUBMIT_HOMEWORK_LPC   = 1013;  // lpc 催完成 巩固练习
    const BUTTON_TYPE_REVISE_HOMEWORK_COMMON   = 1014;  // 通用 催订正巩固练习
    const BUTTON_TYPE_COMPOSITION_REPORT_COMMON  = 1015;  // 通用 作文报告反馈

    const BUTTON_TYPE_ADD_WX_LPC            = 1042;  // lpc 催加微
    const BUTTON_TYPE_SERVICE_ADD_WX_LPC    = 1043;  // lpc 服务期催加微
    const BUTTON_TYPE_HIGHLIGHT_LPC         = 1045;  // lpc 群发高光时刻
    const BUTTON_TYPE_SURVEY                = 1051;  // lpc 群发挖需问卷
    const BUTTON_TYPE_CLASS_REPORT          = 1052;  // lpc 群发课堂报告
    const BUTTON_TYPE_LESSON_REPORT          = 1053;  // lpc 群发课堂报告
    const BUTTON_TYPE_COURSE_REPORT          = 1054;  // lpc 群发课堂报告
    const BUTTON_TYPE_WEEKLY_REPORT          = 1055;  // lpc 群发周报告
    const BUTTON_TYPE_CORRECT_REPORT         = 1056;  // 群发人工批改报告
    const BUTTON_TYPE_EVALUATE               = 1057;  // 群发调研问卷
    const BUTTON_TYPE_COURSE_TIME_TABLE      = 1058;  // 群发课表
    const BUTTON_TYPE_BOTTOM_TEST_FUDAO      = 1059;  // 催完成摸底测（辅导）


    const SEND_TYPE_DEER_ORDER            = 2001;  // 小鹿 催预约
    const SEND_TYPE_DEER_CONTINUE         = 2002;  // 小鹿 催续报
    const BUTTON_TYPE_DEER_PROGRAM_REPORT = 2003;  // 群发课堂报告（编程通用）
    const BUTTON_TYPE_DEER_PROGRAM_REPORT_YOUKETANG = 2004;  // 群发课堂报告（进校）
    const SEND_TYPE_GUANJIA_COMMIT_49            = 3001;  // 管家 催提交
    const SEND_TYPE_GUANJIA_COMMIT_50            = 3002;  // 管家 催提交
    const SEND_TYPE_GUANJIA_COMMIT_51            = 3003;  // 管家 催提交
    const SEND_TYPE_GUANJIA_COMMIT_52            = 3004;  // 管家 催提交
    const SEND_TYPE_GUANJIA_RE_COMMIT_49            = 3005;  // 管家 催订正
    const SEND_TYPE_GUANJIA_RE_COMMIT_50            = 3006;  // 管家 催订正
    const SEND_TYPE_GUANJIA_RE_COMMIT_51            = 3007;  // 管家 催订正
    const SEND_TYPE_GUANJIA_RE_COMMIT_52            = 3008;  // 管家 催订正

    const BUTTON_TYPE_LEARN_REPORT_FD         = 4000;  // 辅导 群发课堂报告
    const BUTTON_TYPE_SPORT_WEEK_REPORT         = 4001;  // 群发周报告

    const BUTTON_TYPE_STAGE_RESULT_FEEDBACK_SENIOR = 4002; //阶段测(高中)
    const BUTTON_TYPE_STAGE_RESULT_FEEDBACK_CARD = 4003; //阶段测(高中)
    const BUTTON_TYPE_RJYL_REPORT = 4005;  // 日积月累报告
    const BUTTON_TYPE_PRONUNCIATION_REPORT = 4006;  // 纠音报告

    const SEND_TYPE_LEARN_REPORT_CAMBRIDGE_ENGLISH     = 4007;  // 群发课堂报告(剑桥英语)
    const SEND_TYPE_PRONUNCIATION_REPORT_GROUP      = 4008;  // 群发个性化纠音报告(自定义分组)
    const SEND_TYPE_VOICE_REPORT_GROUP      = 4009;  // 群发个性化纠音报告(自定义分组)

    const BUTTON_TYPE_UNIT_REPORT_DYD      = 4010;  // 群发单元报告(大阅读)

    const BUTTON_TYPE_SEMESTER_REPORT_SENIOR    = 4004;  // 学期报告反馈（高中）

    const BUTTON_TYPE_CONTRACT_ATTEND_FUDAO     = 5001; // 辅导 权益包催加微
    const BUTTON_TYPE_CONTRACT_ATTEND           = 5002;  // 合约，催到课
    const BUTTON_TYPE_CONTRACT_COURSE_REPORT    = 5003;  // 群发课堂报告
    const BUTTON_TYPE_CONTRACT_LESSON_REPORT    = 5004;  // 群发章节报告
    const BUTTON_TYPE_CONTRACT_AI_OVER_CLASS    = 5005;  // AI催到课
    const BUTTON_TYPE_CONTRACT_REMEDIAL_CLASS   = 5006;  // 催补课
    const BUTTON_TYPE_CONTRACT_CORRECT_REPORT   = 5007; // 人工批改报告

    const BUTTON_TYPE_LEARNING_REPORT           = 6000;  // 发起学情测评
    const BUTTON_TYPE_PROBE_REPORT_QUERY = 6001;  // 学情报告查询
    const BUTTON_TYPE_SEND_TYPE_EDU_PROBE_PDF = 6002;  // 群发学情报告
    const BUTTON_TYPE_SUBJECT_DIAG = 6003;  // 学科诊断
    const BUTTON_TYPE_SUBJECT_DIAG_REPORT = 6004;  // 学科诊断报告
    const BUTTON_TYPE_PERSONAL_LEARN_FEEDBACK = 6005;
    const BUTTON_TYPE_SEND_ASSISTANT_COURSE_QUICK_TASK = 7003;  // 批量建小灶课
    const SEND_TYPE_DEER_CEPING_STAGE1_REPORT = 8001; //群发测评一段报告
    const SEND_TYPE_DEER_CEPING_STAGE2_REPORT = 8002; //群发测评二段报告


    //解析、讲解按钮对应测试类型
    public static $buttonExamTypeMap = [
        self::BUTTON_TYPE_ANALYSE_CHECK_TEST     =>  Api_Exam::BIND_TYPE_POST_TEST,
        self::BUTTON_TYPE_ANALYSE_PREVIEW        =>  Api_Exam::BIND_TYPE_PREVIEW,
        self::BUTTON_TYPE_ANALYSE_IN_CLASS_TEST  =>  Api_Exam::BIND_TYPE_TEST_IN_CLASS,
        self::BUTTON_TYPE_ANALYSE_HOMEWORK       =>  Api_Exam::BIND_TYPE_HOMEWORK,
        self::BUTTON_TYPE_ANALYSE_STAGE_TEST     =>  Api_Exam::BIND_TYPE_STAGE,
        self::BUTTON_TYPE_EXPOUND_CHECK_TEST     =>  Api_Exam::BIND_TYPE_POST_TEST,
        self::BUTTON_TYPE_EXPOUND_PREVIEW        =>  Api_Exam::BIND_TYPE_PREVIEW,
        self::BUTTON_TYPE_EXPOUND_IN_CLASS_TEST  =>  Api_Exam::BIND_TYPE_TEST_IN_CLASS,
        self::BUTTON_TYPE_EXPOUND_HOMEWORK       =>  Api_Exam::BIND_TYPE_HOMEWORK,
        self::BUTTON_TYPE_EXPOUND_STAGE_TEST     =>  Api_Exam::BIND_TYPE_STAGE,
    ];

    /****************** 按钮类常量 end *********************/

    //家访所有任务ID
    const INTERVIEW_TASK_10001 = 10001; //3分钟电话家访
    const INTERVIEW_TASK_10002 = 10002; //完成家访标签
    const INTERVIEW_TASK_10003 = 10003; //鲲鹏微信关联（班课）
    const INTERVIEW_TASK_10003_0 = 100030; //鲲鹏微信关联（专题课）
    const INTERVIEW_TASK_10004 = 10004; //服务号关联且绑定
    const INTERVIEW_TASK_10005 = 10005; //完成摸底测试
    const INTERVIEW_TASK_10006 = 10006; //电话或微信家访完成率(班课)
    const INTERVIEW_TASK_10006_0 = 100060; //电话或微信家访完成率(专题课)
    const INTERVIEW_TASK_10007 = 10007; //电话家访率
    const INTERVIEW_TASK_10008 = 10008; //定级测完成率
    const INTERVIEW_TASK_10009 = 10009; //手动微信关联率
    const INTERVIEW_TASK_10010 = 10010; //微信关联率（总）
    const INTERVIEW_TASK_10011 = 10011;//企业微信工作站手动关联率（学员详情页）- 手动
    const INTERVIEW_TASK_10012 = 10012;//企业微信鲲鹏关联率 - 自动
    const INTERVIEW_TASK_10013 = 10013;//企业微信总关联率 - 手动 + 自动

    //到课所有任务ID
    const INTERVIEW_TASK_20001   = 20001; //预到课标注（班课）                Q：率的计算班课和非班课来自于两个数据源，但是计算方式是一样的，算出来的结果也一样，为什么还要分开数据源获取呢？
    const INTERVIEW_TASK_20001_0 = 200010; //预到课标注（专题课）              A：因为合在一起获取的话，班课里获取不到非班课的历史数据，因为数据组维护的mysql的表是按天来分表的，没法追溯历史数据，等2020年秋学季下掉之后率就可以切换都从数据后台获取了
    const INTERVIEW_TASK_20002   = 20002; //预到课率(班课)
    const INTERVIEW_TASK_20002_0 = 200020; //预到课率(专题课)
    const INTERVIEW_TASK_20003 = 20003; //预习完成率
    const INTERVIEW_TASK_20004 = 20004; //课前预习完成率
    const INTERVIEW_TASK_20005 = 20005; //巩固练习完成率（7天内）
    const INTERVIEW_TASK_20006 = 20006; //到课率
    const INTERVIEW_TASK_20020 = 20020; // 观看率
    const INTERVIEW_TASK_20007 = 20007; //巩固练习完成率（总）
//    const INTERVIEW_TASK_20008 = 20008; //ilab巩固练习提交率（7天内）
//    const INTERVIEW_TASK_20009 = 20009; //ilab巩固练习提交率（总）

    const INTERVIEW_TASK_20011 = 20011; //班主任课前直播到课率
    const INTERVIEW_TASK_20012 = 20012; //班主任课前直播完课率
    const INTERVIEW_TASK_20013 = 20013; //班主任课后直播到课率
    const INTERVIEW_TASK_20014 = 20014; //班主任课后直播完课率

    const HOMEWORK_TASK_20015  = 20015;  //(原版)巩固练习(错题)视频解析查看率
    const HOMEWORK_TASK_20016  = 20016;  //相似题提交率
    const HOMEWORK_TASK_20017  = 20017;  //相似题(错题)视频解析查看率
    const HOMEWORK_TASK_20018  = 20018;  //专题课  到课率5min
    const HOMEWORK_TASK_20019  = 20019;  //专题课  到课率1/4

    const GROUP_BIND_TASK_34011 = 34011; // 进群率


    //日常所有任务ID
    const INTERVIEW_TASK_70001 = 70001; //完成回访标签
    const INTERVIEW_TASK_70002 = 70002; //完成回访电话覆盖
    const INTERVIEW_TASK_70003 = 70003; //阶段报告已读
    const INTERVIEW_TASK_70004 = 70004; //阶段报告解读
    const INTERVIEW_TASK_70005 = 70005; //回访完成
    const RJYL_TASK_70006      = 70006; //日积月累单元任务参与率
    const INTERVIEW_TASK_70007 = 70007; // 转介绍pv转化率
    const MRYL_TASK_70008      = 70008; // 每日一练 周任务参与率

    const FENXIAO_TASK_70009 = 70009; // 转介绍uv转化率
    const FENXIAO_TASK_70010 = 70010; // 转介绍海报生成率
    const FENXIAO_TASK_70011 = 70011; // 转介绍未覆盖学员UV转化率

    const INTERVIEW_TASK_70012 = 70012; // 课后电话回访率
    const INTERVIEW_TASK_70013 = 70013; // 1次课后电话回访率
    const INTERVIEW_TASK_70014 = 70014; // 2次课后电话回访率
    const INTERVIEW_TASK_70015 = 70015; // 3次课后电话回访率
    const INTERVIEW_TASK_70016 = 70016; // 4次课后电话回访率
    const INTERVIEW_TASK_70017 = 70017; // 3分钟电话回访率
    const INTERVIEW_TASK_70018 = 70018; // 3分钟综合回访率
    const INTERVIEW_TASK_70019 = 70019; // 3分钟综合回访达标

    //续报所有任务ID
    const INTERVIEW_TASK_50001 = 50001; //未续报学员电话覆盖
    const INTERVIEW_TASK_50002 = 50002; //未续报学员电话触达
    const INTERVIEW_TASK_50003 = 50003; //未续报学员续报意愿标注
    const INTERVIEW_TASK_50004 = 50004; //二级续报
    const INTERVIEW_TASK_50005 = 50005; //留存率
    const INTERVIEW_TASK_50006 = 50006; //非插班生二级续报率
    const INTERVIEW_TASK_50007 = 50007; //插班生二级续报率
    const INTERVIEW_TASK_50008 = 50008; //班课转化
    const INTERVIEW_TASK_50009 = 50009; //未续报学员电话覆盖（非班课）
    const INTERVIEW_TASK_50010 = 50010; //未续报学员电话触达（非班课）
    const INTERVIEW_TASK_50011 = 50011; //单报学员二级续报率
    const INTERVIEW_TASK_50012 = 50012; //单报学员留存率
    const INTERVIEW_TASK_50013 = 50013; //二级续报且联报率
    const INTERVIEW_TASK_50014 = 50014; //专题课招募率
    const INTERVIEW_TASK_50015 = 50015; //一级续报
    const INTERVIEW_TASK_50016 = 50016; //报价学员二级续报率
    const INTERVIEW_TASK_50017 = 50017; //保价学员二级续报且联报率
    const INTERVIEW_TASK_50018 = 50018; // 特惠课的课后有效退费率
    const INTERVIEW_TASK_50019 = 50019; // 特惠课的排灌班后有效退费率
    const INTERVIEW_TASK_50020 = 50020; //699二级续报率
    const INTERVIEW_TASK_50021 = 50021; //699二级续报且联报率
    const INTERVIEW_TASK_50022 = 50022; //899二级续报率
    const INTERVIEW_TASK_50023 = 50023; //899二级续报且联报率
    const INTERVIEW_TASK_50024 = 50024; //辅导老师满意度问卷 - 服务满意度
    const INTERVIEW_TASK_50025 = 50025; //辅导老师满意度问卷 - 问卷提交率
    const INTERVIEW_TASK_50026 = 50026; //联报学员报同科春3率
    const INTERVIEW_TASK_50027 = 50027; //春1春3联报率
    const INTERVIEW_TASK_50028 = 50028; //春1春3同科单报率
    const INTERVIEW_TASK_50030 = 50030; //初中总预约率
    const INTERVIEW_TASK_50031 = 50031; //初中9元预约率
    const INTERVIEW_TASK_50032 = 50032; //初中10元预约率
    const INTERVIEW_TASK_50029 = 50029; //小学总预约率
    const INTERVIEW_TASK_50033 = 50033; //本学科密训班的报名率


    //扩科相关指标
    const EXPAND_TASK_60001    = 60001;  //联报扩科转化率
    const EXPAND_TASK_60002    = 60002;  //单报扩科转化率

    const EXPAND_TASK_60003    = 60003;  //语文本活动可扩科人数
    const EXPAND_TASK_60004    = 60004;  //数学本活动可扩科人数
    const EXPAND_TASK_60005    = 60005;  //英语本活动可扩科人数
    const EXPAND_TASK_60006    = 60006;  //语文本活动招募率
    const EXPAND_TASK_60007    = 60007;  //数学本活动招募率
    const EXPAND_TASK_60008    = 60008;  //英语本活动招募率


    const ENTERPRISE_WECHAT_COURSE_TOTAL_COVERAGE_RATE_61000 = 61000;
    const ENTERPRISE_WECHAT_COURSE_1V1_COVERAGE_RATE_61001 = 61001;
    const ENTERPRISE_WECHAT_COURSE_1V2_6_COVERAGE_RATE_61002 = 61002;
    const ENTERPRISE_WECHAT_COURSE_1V7_COVERAGE_RATE_61003 = 61003;
    const ENTERPRISE_WECHAT_COURSE_ATTEND_COVERAGE_RATE_61004 = 61004;
    const ENTERPRISE_WECHAT_COURSE_FINISH_COVERAGE_RATE_61005 = 61005;


    const ASSISTANT_COURSE_62001 = 62001; //小灶课综合预约率
    const ASSISTANT_COURSE_62002 = 62002; //小灶课主动预约率
    const ASSISTANT_COURSE_62003 = 62003; //小灶课被动预约率
    const ASSISTANT_COURSE_62004 = 62004; //小灶课到课覆盖率
    const ASSISTANT_COURSE_62005 = 62005; //小灶课到课率
    const ASSISTANT_COURSE_62006 = 62006; //小灶课完课覆盖率
    const ASSISTANT_COURSE_62007 = 62007; //小灶课完课率
    const ASSISTANT_COURSE_62008 = 62008; //小灶课完课率
    const ASSISTANT_COURSE_62009 = 62009; //小灶课完课率
    const ASSISTANT_COURSE_62010 = 62010; //小灶课完课率
    const ASSISTANT_PRONUNCIATION_63001 = 63001; //小灶课完课率
    const ASSISTANT_COURSE_63002 = 63002; //小灶课综合预约率
    const ASSISTANT_COURSE_63003 = 63003; //小灶课主动预约率
    const ASSISTANT_COURSE_63004 = 63004; //小灶课被动预约率
    const ASSISTANT_COURSE_63005 = 63005; //小灶课到课覆盖率
    const ASSISTANT_COURSE_63006 = 63006; //小灶课到课率
    const ASSISTANT_COURSE_63007 = 63007; //小灶课完课覆盖率
    const ASSISTANT_COURSE_63008 = 63008; //小灶课完课率
    const ASSISTANT_COURSE_63009 = 63009; //个性化纠音完成率
    const ASSISTANT_COURSE_63010 = 63010; //配音小达人完成率


    //浣熊任务
    const INTERVIEW_TASK_90001 = 90001; //设备正常率
    const INTERVIEW_TASK_90002 = 90002; //课前作业完成率
    const INTERVIEW_TASK_90003 = 90003; //课后作业完成率
    const INTERVIEW_TASK_90004 = 90004; //整体作业完成率
//    const INTERVIEW_TASK_90005 = 90005; //设备调试率

    //讲题率相关
    const EXPOUND_TASK_81001 = 81001; //摸底测讲解率
//    const EXPOUND_TASK_81002 = 81002; //预习讲解率
//    const EXPOUND_TASK_81003 = 81003; //堂堂测讲解率
//    const EXPOUND_TASK_81004 = 81004; //巩固练习讲解率
//    const EXPOUND_TASK_81005 = 81005; //阶段测讲解率

    //订正率相关
    const AMEND_TASK_82004 = 82004; //巩固练习订正率
    const AMEND_TASK_82005 = 82005; //S+A订正率
    const AMEND_TASK_82006 = 82006; //订正或提交3次巩固练习率

    //完成情况相关（摸底测、预习、巩固练习已有）
    const FINISH_TASK_83003 = 83003; //堂堂测完成率
    const FINISH_TASK_83005 = 83005; //阶段测完成率

    //批改相关
    const HOMEWORK_TASK_71001 = 71001; //巩固练习及时批改率

    // 押题课
//    const YATIKE_TASK_72001 = 72001; // 押题课报名率

    // 通用任务组件常量
    const FEATURE_COMMON_TASK_2 = 2; // 转介绍活动
    const FEATURE_COMMON_TASK_EVALUATE = 4; // 调查问卷
    const FEATURE_COMMON_TASK_SPORT_WEEK_REPORT = 5; // 运动周报告
    const FEATURE_COMMON_TASK_CALL_COUNT_DAYS_FILTER = 6;   // 语音通话次数筛选
    const FEATURE_COMMON_TASK_EXERCISE_NOTE_TASK = 7;   // 语音通话次数筛选

    //模板课程绑定状态Map
    const TMPL_COURSE_NOT_BIND  = 1;
    const TMPL_COURSE_BIND      = 2;
    const TMPL_COURSE_UNTYING   = 3;
    public static $tmplCourseStatusMap = [
        self::TMPL_COURSE_NOT_BIND  => '未绑定',
        self::TMPL_COURSE_BIND      => '已绑定',
        self::TMPL_COURSE_UNTYING   => '已解绑',
    ];

    //方舟可显示课程类型
    public static $courseTypeMap = [
        Zb_Const_Course::TYPE_PRIVATE_LONG  => '班课',
        Zb_Const_Course::TYPE_PRIVATE       => '专题课',
    ];
}
