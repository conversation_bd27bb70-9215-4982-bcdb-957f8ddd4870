<?php
class Service_Data_Deskv1_SenceConf {

    const MAX_LENGTH_LONG = 1000;
    const MAX_LENGTH_SHORT = 500; //

    const FILTER_GROUP = 'group';
    const FILTER_CLASS = 'class';
    const FILTER_SMS = 'sms';

    const SEND_MULTI = 'multi';
    const SEND_SINGLE = 'single';
    const SEND_SMS = 'sms';

    const DIMENSION_TYPE_SCENE  = 'scene';
    const DIMENSION_TYPE_COURSE = 'course';
    const DIMENSION_TYPE_LESSON = 'lesson';

    const CONFIG_KEY_SENDCARTD_ICON = "assistantdesk_send_card_icon_config";
    const CONFIG_KEY_SENDCARTD_ICON_Stage_Result_FeedBack = "assistantdesk_send_card_icon_stage_result_feedback_config";




    /**
     * 家访问卷
     */
    const INTERVIEW_URL_MAP = [
            '2020_2' => [
                'ori'    => 'zyb_8400e73dfebcf12023f9522565a88b9e.png',
                'name'   => 'zyb_8400e73dfebcf12023f9522565a88b9e.png',
                'url'    => 'https://img.zuoyebang.cc/zyb_8400e73dfebcf12023f9522565a88b9e.png',
                'width'  => 750,
                'height' => 1334,
            ],
            '2020_3' => [
                'name'   => 'zyb_d98e099c0653cc78d7e72c5f1e24d941.png',
                'ori'    => 'zyb_d98e099c0653cc78d7e72c5f1e24d941.png',
                'url'    => 'https://img.zuoyebang.cc/zyb_d98e099c0653cc78d7e72c5f1e24d941.png',
                'width'  => 750,
                'height' => 1334,
            ],
            '2021_4' => [
                'name'   => 'zyb_64f51ba726f634655663356c9032280a.png',
                'ori'    => 'zyb_64f51ba726f634655663356c9032280a.png',
                'url'    => 'https://img.zuoyebang.cc/zyb_64f51ba726f634655663356c9032280a.png',
                'width'  => 750,
                'height' => 1334,
            ],
            '2021_3' => [
                'name'   => 'zyb_9eb5ec08be1a7ecb99842c9070806f6f.png',
                'ori'    => 'zyb_9eb5ec08be1a7ecb99842c9070806f6f.png',
                'url'    => 'https://img.zuoyebang.cc/zyb_9eb5ec08be1a7ecb99842c9070806f6f.png',
                'width'  => 750,
                'height' => 1334,
            ],
            '2021_1' => [
                'name'   => 'zyb_0630e8e3d5a600ea014141edf59d3a79.png',
                'ori'    => 'zyb_0630e8e3d5a600ea014141edf59d3a79.png',
                'url'    => 'https://img.zuoyebang.cc/zyb_0630e8e3d5a600ea014141edf59d3a79.png',
                'width'  => 750,
                'height' => 1334,
            ],
            '2021_2' => [
                'name'   => 'zyb_08dc2f9fc6ba024e9aadc376a3b5152b.png',
                'ori'    => 'zyb_08dc2f9fc6ba024e9aadc376a3b5152b.png',
                'url'    => 'https://img.zuoyebang.cc/zyb_08dc2f9fc6ba024e9aadc376a3b5152b.png',
                'width'  => 750,
                'height' => 1334,
            ],
            '2022_1' => [
                'name'   => 'zyb_a8b7caee67b1e7757e73f6e845915be5.png',
                'ori'    => 'zyb_a8b7caee67b1e7757e73f6e845915be5.png',
                'url'    => 'https://img.zuoyebang.cc/zyb_a8b7caee67b1e7757e73f6e845915be5.png',
                'width'  => 750,
                'height' => 1334,
            ],
        ];

    /**
     * 跟课详情反馈开始
     */
    // 预到课请假
    const LABEL_PREATTEND_LEAVE = 1;
    // 到课标签
    const LABEL_ATTEND_L1 = 1;
    const LABEL_ATTEND_L2 = 2;
    const LABEL_ATTEND_L3 = 3;
    const LABEL_ATTEND_L4 = 4;
    const LABEL_ATTEND_L5 = 5;
    // 互动题标签
    const LABEL_INTERACTION_L0 = 0;
    const LABEL_INTERACTION_L1 = 1;
    const LABEL_INTERACTION_L2 = 2;
    const LABEL_INTERACTION_L3 = 3;

    const TIPS_NORMAL = '课程有效学员<span class="sum">%d</span>人';
    const TIPS_WITH_NOTREADY = '课程有效学员<span class="sum">%d</span>人，其中<span class="sum">%d</span>人上课数据未就绪，无法收到图片反馈，建议30min后再发送';

    const TIPS_REPORT = '运动周报告课程有效学员<span class="sum">%d</span>人，其中<span class="sum">%d</span>人周报告数据未就绪，无法收到图片反馈';

    const LABEL_ARRAY = [
        'attend' => [
            self::LABEL_ATTEND_L1 => [
                'name' => '未到课',
                'nlpid' => 10001,
            ],
            self::LABEL_ATTEND_L2 => [
                'name' => '时长不足',
                'nlpid' => 20001,
            ],
            self::LABEL_ATTEND_L3 => [
                'name' => '课中离线',
                'nlpid' => 20004,
            ],
            self::LABEL_ATTEND_L4 => [
                'name' => '频繁进出',
                'nlpid' => 20003,
            ],
            self::LABEL_ATTEND_L5 => [
                'name' => '完整听课',
                'nlpid' => 20002,
            ]
        ],
        'interaction' => [
            self::LABEL_INTERACTION_L0 => [
                'name'  => '参与度低',
                'nlpid' => 30001,
            ],
            self::LABEL_INTERACTION_L1 => [
                'name' => '参与度低',
                'nlpid' => 30001,
            ],
            self::LABEL_INTERACTION_L2 => [
                'name' => '正确率低',
                'nlpid' => 30003,
            ],
            self::LABEL_INTERACTION_L3 => [
                'name' => '正确率高',
                'nlpid' => 30002,
            ],
        ],
        'preattend' => [
            self::LABEL_PREATTEND_LEAVE => [
                'name' => '请假',
                'nlpid' => 40001,
            ],
        ],
        'notready' => [
            1 => [
                'name' => '数据未就绪',
            ],
        ],
    ];
    const OVERVIEW_LABAL    = [
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_ATTEND_OVERVIEW          => [
            'attend_1'               => 0,
            'attend_2'               => 0,
            'attend_5|interaction_0' => 0,
            'attend_5|interaction_1' => 0,
            'attend_5|interaction_3' => 0,
            'attend_5|interaction_2' => 0,
            'attend_4|interaction_0' => 0,
            'attend_4|interaction_1' => 0,
            'attend_4|interaction_3' => 0,
            'attend_4|interaction_2' => 0,
            'attend_3|interaction_0' => 0,
            'attend_3|interaction_1' => 0,
            'attend_3|interaction_3' => 0,
            'attend_3|interaction_2' => 0,
            'preattend_1'            => 0,
            'notready_1'             => 0,
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_ATTEND_OVERVIEW_PLAYBACK => [
            'attend_1' => 0,
            'attend_2' => 0,
            'attend_3' => 0,
            'attend_4' => 0,
            'attend_5' => 0,
            // 'preattend_1' => 0,
            'notready_1' => 0,
        ]
    ];
    const LABEL_NAME_SUFFIX = ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十', '十一', '十二', '十三'];

    /**
     * 跟课详情反馈结束
     */

//    const FILTER_PARAM_TRANS = [
//        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_CUOTIBEN_JIEXI=>[
//            Service_Page_Desk_Filter_GetFilterMap::FILTER_LESSON_SELECT_KEY => 'lessonId'
//        ],
//        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_CUOTIBEN_ZUODA=>[
//            Service_Page_Desk_Filter_GetFilterMap::FILTER_LESSON_SELECT_KEY => 'lessonId'
//        ],
//    ];


    /**
     * 场景化群发筛选配置
     * fileTitle:               图片、文件说明 若不为空展示图片或文件
     * loadRenderImg:           是否加载生成图片组件
     * loadWrongQuestionPdf:    是否加载错题本组件
     */
    const SCENE_CONF = [
        //第一次提测
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_PREVIEW => [
            'name'                  => '催预习',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_MULTI,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_PREVIEW,
            'stuType'               => '未预习',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_PREATTEND => [
            'name'                  => '一键预到课',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_MULTI,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_PREATTEND,
            'stuType'               => '该章节有效学员',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_PLAYBACK => [
            'name'                  => '催回放',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_MULTI,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_PLAYBACK,
            'stuType'               => '未出勤且未看回放',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_NOTES => [
            'name'                  => '发课堂笔记',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_MULTI,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_NOTES,
            'stuType'               => '该章节有效学员',
            'fileTitle'             => '发送文件',
            'fileName'              => '课堂笔记.pdf',
            'fileSubtitle'          => '（当前章节课堂练习，不可编辑）',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_HOMEWORK => [
            'name'                  => '催巩固练习',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_MULTI,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_HOMEWORK,
            'stuType'               => '巩固练习未提交学员',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_SUBMIT_HOMEWORK => [
            'name'                  => '催提交巩固练习',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_MULTI,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_SUBMIT_HOMEWORK,
            'stuType'               => '巩固练习未提交学员',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_SUBMIT_HOMEWORK_LIKE => [
            'name'                  => '催提交相似题',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_MULTI,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_SUBMIT_HOMEWORK_LIKE,
            'stuType'               => '相似题已布置未提交学员',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_SUBMIT_ORAL_QUESTION => [
            'name'                  => '催口述题',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_MULTI,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_SUBMIT_ORAL_QUESTION,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_ORAL_QUESTION_FEEDBACK => [
            'name'                  => '口述题反馈',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_SINGLE,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_ORAL_QUESTION_FEEDBACK,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => 'https://img.zuoyebang.cc/zyb_0bfa8265f8691e86b95450e7307c1193.png',
            'imgTransfer'           => false,
            'loadRenderImg'         => true,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_RJYL => [
            'name'                  => '催日积月累',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_MULTI,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_RJYL,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_MRYL => [
            'name'                  => '催每日一练',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_MULTI,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_MRYL,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_HIGHLIGHT => [
            'name'                  => '群发高光时刻',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_SINGLE,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_HIGHLIGHT,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
//            'imgSrc'                => 'https://img.zuoyebang.cc//zyb_9c62f9bc463fffbdfb60eacc0aa5e738.png',
            'imgSrc'                => 'https://img.zuoyebang.cc/zyb_2f7f1ec0be33f00278e8323224cc76e9.png',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [],
        ],

        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_INTRODUCEPOSTER          => [
            'name'                 => '群发转介绍海报',
            'maxLength'            => self::MAX_LENGTH_LONG,
            'sendMode'             => self::SEND_SINGLE,
            'filterType'           => self::FILTER_CLASS,
            'sendType'             => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_INTRODUCEPOSTER,
            'stuType'              => '',
            'fileTitle'            => '海报封面',
            'fileName'             => '',
            'fileSubtitle'         => '',
            'imgSrc'               => '',
            'imgTransfer'          => false,
            'loadRenderImg'        => true,
            'loadWrongQuestionPdf' => false,
            'sceneFilter'          => [self::WATCH_LESSON_CNT],
        ],

        //第二次提测
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_ATTEND => [
            'name'                  => '催到课',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_MULTI,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_ATTEND,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [],
            'hasSendCard' => [  // 发送卡片能力
                'isShow' => false, // 是否展示
                'canHide' => true, //支持隐藏
                'sendCardField' => '', // 发送卡片链接对应字段
                'defaultPic' => [
                    [
                        'url' => 'https://charge.zuoyebang.cc/fudao_11fac43a2dd1f229a4caef2140ac8ea2.jpg?authorization=bce-auth-v1%2F80054b2251d24cb98eda994c93426d7d%2F2024-03-21T02%3A52%3A50Z%2F-1%2Fhost%2Fe7e50d85c46d31e335a0f8d63a14f05cdf740e7d611e3945ffa8be41d2893a0c'
                    ],
                    [
                        'url' => 'https://charge.zuoyebang.cc/fudao_7e490dd0b1a3d30a9e16c39067f91f55.jpg?authorization=bce-auth-v1%2F80054b2251d24cb98eda994c93426d7d%2F2024-03-21T03%3A01%3A10Z%2F-1%2Fhost%2Ff694b21efab30ac74acde93d4aaa5df8292bc990d11f169afa0237ca1cbf77bc'
                    ],
                    [
                        'url' => 'https://charge.zuoyebang.cc/fudao_7d57561b37e359796482161889e8331f.jpg?authorization=bce-auth-v1%2F80054b2251d24cb98eda994c93426d7d%2F2024-03-21T03%3A01%3A52Z%2F-1%2Fhost%2F79e5670b3d33d5c43e0e4d85e974ccf64cc85045ff626211bb4941b1f95bc373'
                    ]
                ], // 默认封面图片
                'maxPicCnt' => 5, // 最多支持几张默认封面图片
                'defaultTitle' => ['你报名的课程就要开始上课了'], //标题
                'cardVariable' => [], // 卡片支持的变量名
                'introductionVariable' => [], //描述支持的变量
                'defaultIntroduction' => ['点击这里快速进入课堂'] //副标题
            ],
        ],
        // 管家催到课
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_GUANJIA_ATTEND => [
            'name'                  => '催到课',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_MULTI,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_GUANJIA_ATTEND,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [],
            'hasSendCard' => [  // 发送卡片能力
                'isShow' => false, // 是否展示
                'canHide' => true, //支持隐藏
                'sendCardField' => '', // 发送卡片链接对应字段
                'defaultPic' => [
                    [
                        'url' => 'https://charge.zuoyebang.cc/fudao_11fac43a2dd1f229a4caef2140ac8ea2.jpg?authorization=bce-auth-v1%2F80054b2251d24cb98eda994c93426d7d%2F2024-03-21T02%3A52%3A50Z%2F-1%2Fhost%2Fe7e50d85c46d31e335a0f8d63a14f05cdf740e7d611e3945ffa8be41d2893a0c'
                    ],
                    [
                        'url' => 'https://charge.zuoyebang.cc/fudao_7e490dd0b1a3d30a9e16c39067f91f55.jpg?authorization=bce-auth-v1%2F80054b2251d24cb98eda994c93426d7d%2F2024-03-21T03%3A01%3A10Z%2F-1%2Fhost%2Ff694b21efab30ac74acde93d4aaa5df8292bc990d11f169afa0237ca1cbf77bc'
                    ],
                    [
                        'url' => 'https://charge.zuoyebang.cc/fudao_7d57561b37e359796482161889e8331f.jpg?authorization=bce-auth-v1%2F80054b2251d24cb98eda994c93426d7d%2F2024-03-21T03%3A01%3A52Z%2F-1%2Fhost%2F79e5670b3d33d5c43e0e4d85e974ccf64cc85045ff626211bb4941b1f95bc373'
                    ]
                ], // 默认封面图片
                'maxPicCnt' => 5, // 最多支持几张默认封面图片
                'defaultTitle' => ['你报名的课程就要开始上课了'], //标题
                'cardVariable' => [], // 卡片支持的变量名
                'introductionVariable' => [], //描述支持的变量
                'defaultIntroduction' => ['点击这里快速进入课堂'] //副标题
            ],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_7 => [
            'name'                  => '群发预习点评',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_SINGLE,
            'filterType'            => self::FILTER_GROUP,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_7,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [
                self::IS_SEND,
            ],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_ATTEND_OVERVIEW => [
            'name'                  => '跟课详情反馈',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_SINGLE,
            'filterType'            => self::FILTER_GROUP,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_ATTEND_OVERVIEW,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => true,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [
                self::IS_SEND_LESSON,
//                self::IS_L2R,
                self::ATTEND_LIVE_STATUS,
                self::USER_TYPE,
            ],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_ATTEND_OVERVIEW_PLAYBACK => [
            'name'                  => '伴学跟课详情反馈',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_SINGLE,
            'filterType'            => self::FILTER_GROUP,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_ATTEND_OVERVIEW_PLAYBACK,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => true,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [
                self::IS_SEND_LESSON,
//                self::IS_L2R,
                self::ATTEND_LIVE_STATUS,
                self::USER_TYPE,
                self::COMMON_ATTEND_STATUS_BY_DURATION,
                self::COMMON_PLAYBACK_THREE_FIVE_V1,
            ],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_REVISE_HOMEWORK => [
            'name'                  => '催订正巩固练习',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_MULTI,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_REVISE_HOMEWORK,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [
                self::HOMEWORK_LEVEL,
            ],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_REVISE_HOMEWORK_LIKE => [
            'name'                  => '催订正相似题',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_MULTI,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_REVISE_HOMEWORK_LIKE,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [
                self::SIMILAR_HOMEWORK_LEVEL,
            ],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_16 => [
            'name'                  => '催家访问卷',
            'maxLenght'             => self::MAX_LENGTH_SHORT,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_16,
            'stuType'               => '',
            'fileTitle'             => '问卷二维码',
            'fileName'              => '',
            'fileSubtitle'          => '（默认携带发送问卷二维码图片，不可编辑）',
            'imgSrc'                => '', //
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [
                self::IS_SEND,
                self::USER_TYPE,
            ],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_SATISFACTION_QUE => [
            'name'                  => '催满意度问卷',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_MULTI,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_SATISFACTION_QUE,
            'stuType'               => '未填写学员',
            'fileTitle'             => '问卷二维码',
            'fileName'              => '',
            'fileSubtitle'          => '（默认携带发送问卷二维码图片，不可编辑）',
            'imgSrc'                => '', //
            'imgTransfer'           => true,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'editStudentDisable'    => true,
            'sceneFilter'           => [
                self::IS_SEND,
//                self::QUE_STATUS,
            ],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_CORRECT_FEEDBACK => [
            'name'                  => '批改结果反馈',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_MULTI,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_CORRECT_FEEDBACK,
            'stuType'               => '',
            'fileTitle'             => '批改结果图片反馈示例',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_CORRECT_FEEDBACK_DEER => [
            'name'                  => '批改结果反馈（小鹿）',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_MULTI,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_CORRECT_FEEDBACK_DEER,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_CORRECT_FEEDBACK_LIKE => [
            'name'                  => '相似题批改结果反馈',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_MULTI,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_CORRECT_FEEDBACK_LIKE,
            'stuType'               => '',
            'fileTitle'             => '相似题批改结果图片反馈示例',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [
                self::IS_SEND,
                self::SIMILAR_HOMEWORK_SUBMIT_NUM,
                self::SIMILAR_HOMEWORK_LEVEL,
            ],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_COMPOSITION_REPORT => [
            'name'                  => '作文报告反馈',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_MULTI,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_COMPOSITION_REPORT,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '', //
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_SUBMIT_MONTHLY_EXAM => [
            'name'                  => '催月考',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_MULTI,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_SUBMIT_MONTHLY_EXAM,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '', //在 studentFilter 接口处理
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_CUOTIBEN_JIEXI => [
            'name'                  => '群发解析版错题本',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_SINGLE,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_CUOTIBEN_JIEXI,
            'stuType'               => '',
            'fileTitle'             => '发送文件',
            'fileName'              => '',
            'fileSubtitle'          => '（当前章节课堂练习，不可编辑）',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => true,
            'sceneFilter'           => [],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_CUOTIBEN_ZUODA => [
            'name'                  => '群发作答版错题本',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_SINGLE,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_CUOTIBEN_ZUODA,
            'stuType'               => '',
            'fileTitle'             => '发送文件',
            'fileName'              => '',
            'fileSubtitle'          => '（当前章节课堂练习，不可编辑）',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => true,
            'sceneFilter'           => [],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_EXERCISE_NOTE_TASK_JIEXI => [
            'name'                  => '群发解析版错题任务',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_SINGLE,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_EXERCISE_NOTE_TASK_JIEXI,
            'stuType'               => '',
            'fileTitle'             => '发送文件',
            'fileName'              => '',
            'fileSubtitle'          => '（当前章节课堂练习，不可编辑）',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => true,
            'sceneFilter'           => [],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_EXERCISE_NOTE_TASK_ZUODA => [
            'name'                  => '群发作答版错题任务',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_SINGLE,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_EXERCISE_NOTE_TASK_ZUODA,
            'stuType'               => '',
            'fileTitle'             => '发送文件',
            'fileName'              => '',
            'fileSubtitle'          => '（当前章节课堂练习，不可编辑）',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => true,
            'sceneFilter'           => [],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_MONTHLYEXAM_REPORT => [
            'name'                  => '反馈阶段测报告',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_SINGLE,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_MONTHLYEXAM_REPORT,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => 'https://img.zuoyebang.cc/zyb_f812b8a28f42d8ee05e0a71bbc0ba020.png',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [
                self::IS_SEND_LESSON,
            ],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_STAGE_SUBMIT => [
            'name'                  => '催提交阶段测',
            'maxLength'             => self::MAX_LENGTH_SHORT,
            'sendMode'              => self::SEND_MULTI,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_STAGE_SUBMIT,
            'stuType'               => '未提交阶段测',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [
                self::IS_SEND_LESSON,
            ],
        ],

        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_STAGE_RESULT_FEEDBACK => [
            'name'                  => '阶段测结果反馈',
            'maxLength'             => self::MAX_LENGTH_SHORT,
            'sendMode'              => self::SEND_MULTI,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_STAGE_RESULT_FEEDBACK,
            'stuType'               => '已提交阶段测',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [
                self::IS_SEND_LESSON,
                self::IS_STAGE_REPORT_COMPLETE_LESSON,
                self::STAGE_REPORT_SCORE_LESSON,
                self::STAGE_REPORT_IS_READ
            ],
        ],

        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_STAGE_RESULT_FEEDBACK_SENIOR => [
            'name'                  => '阶段测结果反馈(高中)',
            'maxLength'             => self::MAX_LENGTH_SHORT,
            'sendMode'              => self::SEND_MULTI,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_STAGE_RESULT_FEEDBACK_SENIOR,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [
                self::IS_SEND_LESSON,
                self::IS_STAGE_REPORT_COMMIT_LESSON,
                self::IS_STAGE_REPORT_COMPLETE_LESSON_SENIOR,
                self::STAGE_REPORT_SCORE_LESSON,
            ],
        ],


        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_PREVIEW_LPC => [
            'name'                  => '催预习',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_SINGLE,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_PREVIEW_LPC,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_BOTTOM_TEST => [
            'name'                  => '催完成摸底测',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_SINGLE,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_BOTTOM_TEST,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [
                self::BOTTOM_TEST_TYPE,
                self::BOTTOM_TEST_SCORE,
                self::IS_SEND,
            ],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_BOTTOM_TEST_FUDAO => [
            'name'                  => '催完成摸底测（辅导）',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_SINGLE,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_BOTTOM_TEST_FUDAO,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [
                self::BOTTOM_TEST_FUDAO_TYPE,
            ],
            'hasSendCard' => [  // 发送卡片能力
                'isShow' => true, // 是否展示
                'sendCardField' => '#摸底测链接#', // 发送卡片链接对应字段
                'defaultPic' => [
                    ["url"=>"https://img.zuoyebang.cc/zyb_d050f4cda11335e5e4b97b1dc03e6709.png","iconType"=>4],
                    ["url"=>"https://img.zuoyebang.cc/zyb_7c1382c118a82d8e5ba6cc9918f091ab.png","iconType"=>3],
//                    ["url"=>"https://img.zuoyebang.cc/zyb_08e443d876b668c16b006028848e176c.png","iconType"=>2],
                ],
                'maxPicCnt' => 5, // 最多支持几张默认封面图片
                'defaultTitle' => ['#学生名#', '请查收摸底测考试链接'],
                'cardVariable' => ['学生名', '课程名称','学科'], // 卡片支持的变量名
                'introductionVariable' => ['学生名', '课程名称','学科'], //描述支持的变量
                'defaultIntroduction' => ['点击链接，进行作答'] //描述支持的变量
            ],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_PERIOD_EXAM => [
            'name'                  => '催完成阶段测',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_SINGLE,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_PERIOD_EXAM,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_EXERCISE_REPORT => [
            'name'                  => '群发巩固练习报告',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_SINGLE,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_EXERCISE_REPORT,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_SERVICE_ADD_WX_LPC => [
            'name'                  => '服务期催加微',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_SINGLE,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_SERVICE_ADD_WX_LPC,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_ATTEND_LPC => [
            'name'                  => '催到课',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_SINGLE,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_ATTEND_LPC,
            'stuType'               => '未进入直播间且未请假学员',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [
                self::LPC_PRE_ATTEND_STATUS,
            ],
            'hasSendCard' => [  // 发送卡片能力
                'isShow' => false, // 是否展示
                'canHide' => true, //支持隐藏
                'sendCardField' => '', // 发送卡片链接对应字段
                'defaultPic' => [
                    [
                        'url' => 'https://charge.zuoyebang.cc/fudao_11fac43a2dd1f229a4caef2140ac8ea2.jpg?authorization=bce-auth-v1%2F80054b2251d24cb98eda994c93426d7d%2F2024-03-21T02%3A52%3A50Z%2F-1%2Fhost%2Fe7e50d85c46d31e335a0f8d63a14f05cdf740e7d611e3945ffa8be41d2893a0c'
                    ],
                    [
                        'url' => 'https://charge.zuoyebang.cc/fudao_7e490dd0b1a3d30a9e16c39067f91f55.jpg?authorization=bce-auth-v1%2F80054b2251d24cb98eda994c93426d7d%2F2024-03-21T03%3A01%3A10Z%2F-1%2Fhost%2Ff694b21efab30ac74acde93d4aaa5df8292bc990d11f169afa0237ca1cbf77bc'
                    ],
                    [
                        'url' => 'https://charge.zuoyebang.cc/fudao_7d57561b37e359796482161889e8331f.jpg?authorization=bce-auth-v1%2F80054b2251d24cb98eda994c93426d7d%2F2024-03-21T03%3A01%3A52Z%2F-1%2Fhost%2F79e5670b3d33d5c43e0e4d85e974ccf64cc85045ff626211bb4941b1f95bc373'
                    ]
                ], // 默认封面图片
                'maxPicCnt' => 5, // 最多支持几张默认封面图片
                'defaultTitle' => ['你报名的课程就要开始上课了'], //标题
                'cardVariable' => [], // 卡片支持的变量名
                'introductionVariable' => [], //描述支持的变量
                'defaultIntroduction' => ['点击这里快速进入课堂'] //副标题
            ],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_PLAYBACK_LPC => [
            'name'                  => '催回放',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_SINGLE,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_PLAYBACK_LPC,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [
                self::LPC_PRE_ATTEND_STATUS,
            ],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_NOTES_LPC => [
            'name'                  => '群发课堂笔记',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_MULTI,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_NOTES_LPC,
            'stuType'               => '该章节有效学员',
            'fileTitle'             => '发送文件',
            'fileName'              => '课堂笔记.pdf',
            'fileSubtitle'          => '（当前章节课堂练习，不可编辑）',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [
                self::SYSTEM_INTENTION,
                self::INTENTION_STATUS,
                self::PREVIEW_STATUS,
                self::ATTEND_STATUS,
                self::FINISHED_STATUS,
                self::TRANSFER_STATUS,
            ],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_BOTTOM_TEST_REPORT => [
            'name'                  => '群发摸底测报告（LPC）',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_SINGLE,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_BOTTOM_TEST_REPORT,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [
                self::SYSTEM_INTENTION,
                self::INTENTION_STATUS,
                self::PREVIEW_STATUS,
                self::TRANSFER_STATUS,
                self::BOTTOM_TEST_TYPE,
                self::IS_SEND,
                self::CITY_LEVEL,
            ],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_BOTTOM_TEST_REPORT_FD => [
            'name'                  => '群发摸底测报告（辅导）',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_SINGLE,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_BOTTOM_TEST_REPORT_FD,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [
                self::IS_SEND,
                self::BOTTOM_TEST_FUDAO_TYPE,
            ],
            'urlCaptureSrcMap' => [
                '阶段测报告图片' => ['https://img.zuoyebang.cc/zyb_15ece1ea1190f8144e38436a8ee9148b.png']
            ],
            'hasSendCardMap' => [
                '阶段测报告卡片' => [
                    'isShow' => true,
                    'sendCardField' => '#阶段测报告链接#',
                    'defaultPic' => [
                        ["url"=>"https://img.zuoyebang.cc/zyb_d050f4cda11335e5e4b97b1dc03e6709.png","iconType"=>4],
                        ["url"=>"https://img.zuoyebang.cc/zyb_7c1382c118a82d8e5ba6cc9918f091ab.png","iconType"=>3],
//                        ["url"=>"https://img.zuoyebang.cc/zyb_08e443d876b668c16b006028848e176c.png","iconType"=>2],
                        ["url"=>"https://img.zuoyebang.cc/zyb_e8e9fd911f851fd27943e900e9e2a951.png","iconType"=>8],
                    ],
                    'maxPicCnt' => 5,
                    'defaultTitle' => ['请查收', '#学生名#', '的阶段测自评'],
                    'cardVariable' => ["学生名", "课程名称", "学科"],
                    'introductionVariable' => ["学生名", "课程名称", "学科"],
                    'defaultIntroduction' => ['点击查看孩子阶段测自评分析及近期表现情况'],
                ]
            ],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_SUBMIT_HOMEWORK_LPC => [
            'name'                  => '催完成巩固练习',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_SINGLE,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_SUBMIT_HOMEWORK_LPC,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [
                self::LPC_PRE_ATTEND_STATUS,

            ],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_REVISE_HOMEWORK_COMMON => [
            'name'                  => '催订正巩固练习(通用)',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_SINGLE,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_REVISE_HOMEWORK_COMMON,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [
                self::COMMON_HOMEWORK_LEVEL,
            ],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_COMPOSITION_REPORT_COMMON => [
            'name'                  => '作文报告反馈(通用)',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_MULTI,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_COMPOSITION_REPORT_COMMON,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [
                self::COMMON_HOMEWORK_LEVEL,
            ],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_ADD_WX_LPC => [
            'name'                  => '催加微',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_SINGLE,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_ADD_WX_LPC,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_HIGHLIGHT_LPC => [
            'name'                  => '群发高光时刻',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_SINGLE,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_HIGHLIGHT_LPC,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [
                self::SYSTEM_INTENTION,
                self::INTENTION_STATUS,
                self::TRANSFER_STATUS,
            ],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_SURVEY => [
            'name'                  => '群发挖需问卷',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_SINGLE,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_SURVEY,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [
                self::SYSTEM_INTENTION,
                self::INTENTION_STATUS,
                self::TRANSFER_STATUS,
            ],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_CLASS_REPORT => [
            'name'                  => '群发课堂报告',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_SINGLE,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_CLASS_REPORT,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [
                self::SYSTEM_INTENTION,
                self::INTENTION_STATUS,
                self::PREVIEW_STATUS,
                self::ATTEND_STATUS,
                self::FINISHED_STATUS,
                self::TRANSFER_STATUS,
                self::IS_SEND_LESSON,
            ],
            'urlCaptureSrcMap' => [
                '课堂报告图片' => ['https://img.zuoyebang.cc/zyb_86217c1b2c54b628b7ff9b7e8548202b.png']
            ],
            'hasSendCardMap' => [
                '课堂报告卡片' => [
                    'isShow' => true,
                    'sendCardField' => '#课堂报告链接#',
                    'defaultPic' => [
                        ["url"=>"https://img.zuoyebang.cc/zyb_d050f4cda11335e5e4b97b1dc03e6709.png","iconType"=>4],
                        ["url"=>"https://img.zuoyebang.cc/zyb_7c1382c118a82d8e5ba6cc9918f091ab.png","iconType"=>3],
//                        ["url"=>"https://img.zuoyebang.cc/zyb_08e443d876b668c16b006028848e176c.png","iconType"=>2],
                        ["url"=>"https://img.zuoyebang.cc/zyb_e8e9fd911f851fd27943e900e9e2a951.png","iconType"=>8],
                        ["url"=>"https://img.zuoyebang.cc/zyb_8522a2787f5824a8591e95975d5ee499.png","iconType"=>10],
                    ],
                    'maxPicCnt' => 5,
                    'defaultTitle' => ['#学生名#', '请查收你的课堂报告'],
                    'cardVariable' => ["学生名", "课程名称", "学科"],
                    'introductionVariable' => ["学生名", "课程名称", "学科"],
                    'defaultIntroduction' => ["点击这里，快速查看"],
                ]
            ]
        ],

        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_DEER_ORDER => [
            'name'                  => '催预约',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_SINGLE,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_DEER_ORDER,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [
                self::IS_SEND,
            ],
        ],

        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_DEER_CONTINUE => [
            'name'                  => '催续报',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_SINGLE,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_DEER_CONTINUE,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [
                self::IS_SEND,
            ],
        ],

        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_GUANJIA_COMMIT_49 => [
            'name'                  => '催提交堂堂练',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_SINGLE,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_GUANJIA_COMMIT_49,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [
                self::IS_SEND_LESSON,
            ],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_GUANJIA_COMMIT_50 => [
            'name'                  => '催提交举一反三',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_SINGLE,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_GUANJIA_COMMIT_50,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [
                self::IS_SEND_LESSON,
            ],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_GUANJIA_COMMIT_51 => [
            'name'                  => '催提交能力提升',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_SINGLE,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_GUANJIA_COMMIT_51,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [
                self::IS_SEND_LESSON,
            ],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_GUANJIA_COMMIT_52 => [
            'name'                  => '催提交薄弱专项',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_SINGLE,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_GUANJIA_COMMIT_52,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [
                self::IS_SEND_LESSON,
            ],
        ],

        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_GUANJIA_RE_COMMIT_49 => [
            'name'                  => '催订正交堂堂练',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_SINGLE,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_GUANJIA_RE_COMMIT_49,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [
                self::IS_SEND_LESSON,
            ],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_GUANJIA_RE_COMMIT_50 => [
            'name'                  => '催订正举一反三',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_SINGLE,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_GUANJIA_RE_COMMIT_50,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [
                self::IS_SEND_LESSON,
            ],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_GUANJIA_RE_COMMIT_51 => [
            'name'                  => '催订正能力提升',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_SINGLE,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_GUANJIA_RE_COMMIT_51,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [
                self::IS_SEND_LESSON,
            ],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_GUANJIA_RE_COMMIT_52 => [
            'name'                  => '催订正薄弱专项',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_SINGLE,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_GUANJIA_RE_COMMIT_52,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [
                self::IS_SEND_LESSON,
            ],
        ],

//            const BUTTON_TYPE_LPC_SMS_ADD_WX       = 65;//发送用户加微短信 65
//    const BUTTON_TYPE_LPC_SMS_ADD_WX_APPLY = 66;//发送加用户微信申请 66
//    const BUTTON_TYPE_LPC_SMS_ADD_WX_CARD  = 67;//发送用户名片给我 67
//    const BUTTON_TYPE_LPC_SMS_ATTEND       = 68;//催到课短信 68
        // 发短信逻辑
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_LPC_SMS_ADD_WX => [
            'name'                  => '发送用户加微短信',
            'subtitle'              => '引导主动加微短信模版',
            'subtitleDesc'          => '',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_SMS,
            'filterType'            => self::FILTER_SMS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_LPC_SMS_ADD_WX,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [
            ],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_LPC_SMS_ADD_WX_APPLY => [
            'name'                  => '发送加用户微信申请',
            'subtitle'              => '引导通过好友申请短信模版',
            'subtitleDesc'          => '',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_SMS,
            'filterType'            => self::FILTER_SMS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_LPC_SMS_ADD_WX_APPLY,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [
            ],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_LPC_SMS_ADD_WX_CARD => [
            'name'                  => '发送用户名片给我',
            'subtitle'              => '向我推送学员的名片',
            'subtitleDesc'          => '此能力将会通过已添加当前学员的公司企微机器人账号向您发送学员的名片，并自动帮您提交好友申请，请及时前往企微查看',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_SMS,
            'filterType'            => self::FILTER_SMS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_LPC_SMS_ADD_WX_CARD,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [
                self::WXCARD_SEND_TIMES,
            ],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_LPC_SMS_ATTEND => [
            'name'                  => '催到课短信',
            'subtitle'              => '短信模版',
            'subtitleDesc'          => '',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_SMS,
            'filterType'            => self::FILTER_SMS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_LPC_SMS_ATTEND,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [
                self::LPC_PRE_ATTEND_STATUS,
            ],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_LPC_SMS_AI_ATTED => [
            'name'                  => 'AI催到课短信',
            'subtitle'              => '短信模版',
            'subtitleDesc'          => '',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_SMS,
            'filterType'            => self::FILTER_SMS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_LPC_SMS_AI_ATTED,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [],
        ],

        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_LEARN_REPORT_FD => [
            'name'                  => '群发课堂报告（辅导)',
            'subtitle'              => '短信模版',
            'subtitleDesc'          => '',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_SMS,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_LEARN_REPORT_FD,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [
                self::IS_SEND_LESSON,
            ],
            'urlCaptureSrcMap' => [
                '课堂报告图片' => ['https://img.zuoyebang.cc/zyb_86217c1b2c54b628b7ff9b7e8548202b.png']
            ],
            'hasSendCardMap' => [
                '课堂报告卡片' => [
                    'isShow' => true,
                    'sendCardField' => '#课堂报告链接#',
                    'defaultPic' => [
                        ["url"=>"https://img.zuoyebang.cc/zyb_d050f4cda11335e5e4b97b1dc03e6709.png","iconType"=>4],
                        ["url"=>"https://img.zuoyebang.cc/zyb_7c1382c118a82d8e5ba6cc9918f091ab.png","iconType"=>3],
//                        ["url"=>"https://img.zuoyebang.cc/zyb_08e443d876b668c16b006028848e176c.png","iconType"=>2],
                        ["url"=>"https://img.zuoyebang.cc/zyb_e8e9fd911f851fd27943e900e9e2a951.png","iconType"=>8],
                        ["url"=>"https://img.zuoyebang.cc/zyb_8522a2787f5824a8591e95975d5ee499.png","iconType"=>10],
                        ["url"=>"https://img.zuoyebang.cc/zyb_a7a61fa61cb81a8c32afb1fb3e42b245.png","iconType"=>11],
                    ],
                    'maxPicCnt' => 5,
                    'defaultTitle' => ['#学生名#', '请查收你的课堂报告'],
                    'cardVariable' => ["学生名", "课程名称", "学科"],
                    'introductionVariable' => ["学生名", "课程名称", "学科"],
                    'defaultIntroduction' => ["点击这里，快速查看"],
                ]
            ]
        ],

        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_DEER_CEPING_STAGE1_REPORT => [
            'name'                  => '进校测评报告群发(一段)',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_SINGLE,
            'filterType'            => self::FILTER_GROUP,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_DEER_CEPING_STAGE1_REPORT,
            'sendGroupMessage'      => true,
            'sceneFilter'           => [
            ],
            "groupItems" => [
                ["value" => Service_Page_DeskV1_SendMsg_GroupFilter::CUSTOM_GROUP_DEFAULT, "label" => "不分组", "type" => "normal"],
                ["value" => Service_Page_DeskV1_SendMsg_GroupFilter::GROUP_CEPING_TYPE, "label" => "按类别分组", "type" => "normal"],
            ]
        ],

        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_DEER_CEPING_STAGE2_REPORT => [
            'name'                  => '进校测评报告群发(二段)',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_SINGLE,
            'filterType'            => self::FILTER_GROUP,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_DEER_CEPING_STAGE2_REPORT,
            'sendGroupMessage'      => true,
            'sceneFilter'           => [
            ],
            'hasSendCardMap' => [
                '二段报告卡片' => [
                    'isShow' => true,
                    'sendCardField' => '#进校测评二段报告链接#',
                    'defaultPic' => [
                        ["url" => "https://img.zuoyebang.cc/zyb_334051c80b86b2fe5f4a832af2431155.png"],
                    ],
                    'showPic' => true,
                    'maxPicCnt' => 1,
                    'defaultTitle' => ['#学生名#', '请查收你的测评报告'],
                    'cardVariable' => ["课程名称", "学生名", '辅导老师真名'],
                    'introductionVariable' => ["课程名称", "学生名", '辅导老师真名'],
                    'defaultIntroduction' => ["点击这里，快速查看"],
                    'editIntroduction' => true
                ]
            ],
            "groupItems" => [
                ["value" => Service_Page_DeskV1_SendMsg_GroupFilter::CUSTOM_GROUP_DEFAULT, "label" => "不分组", "type" => "normal"],
                ["value" => Service_Page_DeskV1_SendMsg_GroupFilter::GROUP_CEPING_LEVEL, "label" => "按评级分组", "type" => "normal"],
            ]
        ],

        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_SPORT_WEEK_REPORT => [
            'name'                  => '群发运动周报告',
            'subtitle'              => '短信模版',
            'subtitleDesc'          => '',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_SINGLE,
            'filterType'            => self::FILTER_GROUP,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_SPORT_WEEK_REPORT,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_UNIT_REPORT_DYD => [
            'name'                  => '群发单元报告（大阅读)',
            'subtitle'              => '',
            'subtitleDesc'          => '',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_SMS,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_UNIT_REPORT_DYD,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [
                self::IS_SEND_LESSON,

            ],
            'urlCaptureSrcMap' => [
                '单元报告图片' => ['https://img.zuoyebang.cc/zyb_a61bf80cdfafb24c5db0f9cfc609429a.png']
            ],
            'hasSendCardMap' => [
                '单元报告卡片' => [
                    'isShow' => true,
                    'sendCardField' => '#单元报告链接#',
                    'defaultPic' => [
                        //["url"=>"https://img.zuoyebang.cc/zyb_d050f4cda11335e5e4b97b1dc03e6709.png","iconType"=>4],
                        ["url"=>"https://img.zuoyebang.cc/zyb_7c1382c118a82d8e5ba6cc9918f091ab.png","iconType"=>3],
                        //["url"=>"https://img.zuoyebang.cc/zyb_08e443d876b668c16b006028848e176c.png","iconType"=>2],
                        //["url"=>"https://img.zuoyebang.cc/zyb_e8e9fd911f851fd27943e900e9e2a951.png","iconType"=>8],
                        //["url"=>"https://img.zuoyebang.cc/zyb_8522a2787f5824a8591e95975d5ee499.png","iconType"=>10],
                        ["url"=>"https://img.zuoyebang.cc/zyb_a7a61fa61cb81a8c32afb1fb3e42b245.png","iconType"=>11],
                    ],
                    'maxPicCnt' => 5,
                    'defaultTitle' => ['#学生名#', '请查收你的专属单元报告'],
                    'cardVariable' => ["学生名", "课程名称", "学科"],
                    'introductionVariable' => ["学生名", "课程名称", "学科"],
                    'defaultIntroduction' => ["点击这里，快速查看"],
                ]
            ]
        ],

        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_COURSE_REPORT => [
            'name'                  => '群发课堂报告（小鹿）',
            'subtitle'              => '群发课程报告（小鹿）',
            'subtitleDesc'          => '',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_SINGLE,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_COURSE_REPORT,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [
            ],
        ],

        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_LESSON_REPORT => [
            'name'                  => '群发章节报告',
            'subtitle'              => '群发章节报告',
            'subtitleDesc'          => '',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_SINGLE,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_LESSON_REPORT,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [
                self::IS_SEND_LESSON,
            ],
        ],

        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_WEEKLY_REPORT => [
            'name'                  => '群发周报告（小鹿）',
            'subtitle'              => '群发周报告（小鹿）',
            'subtitleDesc'          => '',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_SINGLE,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_WEEKLY_REPORT,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [
            ],
        ],
        // AI催完课
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_CONTRACT_AI_OVER_CLASS => [
            'name'                  => 'AI催完课',
            'subtitle'              => 'AI催完课',
            'subtitleDesc'          => '',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_MULTI,
            'filterType'            => self::FILTER_SMS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_CONTRACT_AI_OVER_CLASS,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [],
        ],
        // 催补课
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_CONTRACT_REMEDIAL_CLASS => [
            'name'                  => '催补课',
            'subtitle'              => '催补课',
            'subtitleDesc'          => '',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_MULTI,
            'filterType'            => self::FILTER_SMS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_CONTRACT_REMEDIAL_CLASS,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [],
        ],

        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_LEARNING_REPORT => [
            'name'                  => '发起学情测评（高中）',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_SINGLE,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_LEARNING_REPORT,
            'stuType'               => '',
            'fileTitle'             => '触达物料',
            'fileName'              => '',
            'fileSubtitle'          => '二维码触达',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => true,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [
                self::IS_SEND,
            ],
            'hasSendCard' => [  // 发送卡片能力
                'isShow' => true, // 是否展示
                'sendCardField' => '#学情测评链接#', // 发送卡片链接对应字段
                'defaultPic' => [
                    ["url"=>"https://img.zuoyebang.cc/zyb_d050f4cda11335e5e4b97b1dc03e6709.png","iconType"=>4],
                    ["url"=>"https://img.zuoyebang.cc/zyb_7c1382c118a82d8e5ba6cc9918f091ab.png","iconType"=>3],
//                    ["url"=>"https://img.zuoyebang.cc/zyb_08e443d876b668c16b006028848e176c.png","iconType"=>2],
                    ["url"=>"https://img.zuoyebang.cc/zyb_e8e9fd911f851fd27943e900e9e2a951.png","iconType"=>8],
                ], // 默认封面图片
                'maxPicCnt' => 5, // 最多支持几张默认封面图片
                'defaultTitle' => ['#学生名#', '请查收你的学情测评链接'],
                'cardVariable' => ['学生名', '辅导老师真名'], // 卡片支持的变量名
                'introductionVariable' => ['学生名', '辅导老师真名'], //描述支持的变量
                'defaultIntroduction' => ['点击这里，快速查看'] //描述支持的变量
            ],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_SUBJECT_DIAG => [
            'name'                  => '群发学科测评（初中）',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_SINGLE,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_SUBJECT_DIAG,
            'stuType'               => '',
            'fileTitle'             => '触达物料',
            'fileName'              => '',
            'fileSubtitle'          => '二维码触达',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => true,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [
                self::IS_SEND,
            ],
            'hasSendCard' => [  // 发送卡片能力
                'isShow' => true, // 是否展示
                'sendCardField' => '#学科测评链接#', // 发送卡片链接对应字段
                'defaultPic' => [
                    ["url"=>"https://img.zuoyebang.cc/zyb_d050f4cda11335e5e4b97b1dc03e6709.png","iconType"=>4],
                    ["url"=>"https://img.zuoyebang.cc/zyb_7c1382c118a82d8e5ba6cc9918f091ab.png","iconType"=>3],
//                    ["url"=>"https://img.zuoyebang.cc/zyb_08e443d876b668c16b006028848e176c.png","iconType"=>2],
                    ["url"=>"https://img.zuoyebang.cc/zyb_e8e9fd911f851fd27943e900e9e2a951.png","iconType"=>8],
                ], // 默认封面图片
                'maxPicCnt' => 5, // 最多支持几张默认封面图片
                'defaultTitle' => ['#学生名#', '请查收你的学科诊断测评链接'],
                'cardVariable' => ['学生名', '辅导老师真名'], // 卡片支持的变量名
                'introductionVariable' => ['学生名', '辅导老师真名'], //描述支持的变量
                'defaultIntroduction' => ['点击这里，作答测评'] //描述支持的变量
            ],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_PROBE_REPORT_QUERY => [
            'name'                  => '学情报告查询',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_SINGLE,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_PROBE_REPORT_QUERY,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [],
        ],
        // 群发人工报告
        Assistant_Ds_WxMessageSendRecord::sEND_TYPE_CORRECT_REPORT => [
            'name'                  => '群发AI课章节报告（人工）',
            'subtitle'              => '群发AI课章节报告（人工）',
            'subtitleDesc'          => '',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_SINGLE,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::sEND_TYPE_CORRECT_REPORT,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [],
        ],
        // 群发调研问卷
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_EVALUATE => [
            'name'                  => '群发调研问卷',
            'subtitle'              => '群发调研问卷',
            'subtitleDesc'          => '',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_SINGLE,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_EVALUATE,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => true,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [
                self::IS_SUBMIT_EVALUATE,
                self::USER_TYPE,
            ],
        ],
        // 群发课表
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_COURSE_TIME_TABLE => [
            'name'                  => '群发课表',
            'subtitle'              => '群发课表',
            'subtitleDesc'          => '',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_SINGLE,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_COURSE_TIME_TABLE,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => true,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [
                self::IS_SEND,
                self::USER_TYPE,
            ],
        ],
        // 快速拉群
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_QW_ADD_GROUP => [
            'name'                  => '快速拉群',
            'subtitle'              => '快速拉群',
            'subtitleDesc'          => '',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_SINGLE,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_QW_ADD_GROUP,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [
                self::IS_DEVICE_DATA_GROUP_BIND_STUDENT,
            ],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_DEER_PROGRAM_REPORT => [
            'name'                  => '群发课堂报告（编程通用）',
            'subtitle'              => '群发课堂报告（编程通用）',
            'subtitleDesc'          => '',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_SINGLE,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_DEER_PROGRAM_REPORT,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [
                self::IS_SEND_LESSON,
                self::DEER_HOMEWORK_LEVEL,
                self::DEER_PROGRAM_HOMEWORK_STATUS,
                self::IN_CLASS_INTERACTIVE_QUESTION_PARTICIPATION_RATE,
                self::IN_CLASS_INTERACTIVE_QUESTION_ACCURACY_RATE
            ],
            'hasSendCard' => [  // 发送卡片能力
                'isShow' => true, // 是否展示
                'sendCardField' => '#小鹿编程课堂报告#', // 发送卡片链接对应字段
                'defaultPic' => [
                    [
                        'url' => 'https://charge.zuoyebang.cc/fudao_11fac43a2dd1f229a4caef2140ac8ea2.jpg?authorization=bce-auth-v1%2F80054b2251d24cb98eda994c93426d7d%2F2024-03-21T02%3A52%3A50Z%2F-1%2Fhost%2Fe7e50d85c46d31e335a0f8d63a14f05cdf740e7d611e3945ffa8be41d2893a0c'
                    ],
                    [
                        'url' => 'https://charge.zuoyebang.cc/fudao_7e490dd0b1a3d30a9e16c39067f91f55.jpg?authorization=bce-auth-v1%2F80054b2251d24cb98eda994c93426d7d%2F2024-03-21T03%3A01%3A10Z%2F-1%2Fhost%2Ff694b21efab30ac74acde93d4aaa5df8292bc990d11f169afa0237ca1cbf77bc'
                    ],
                    [
                        'url' => 'https://charge.zuoyebang.cc/fudao_7d57561b37e359796482161889e8331f.jpg?authorization=bce-auth-v1%2F80054b2251d24cb98eda994c93426d7d%2F2024-03-21T03%3A01%3A52Z%2F-1%2Fhost%2F79e5670b3d33d5c43e0e4d85e974ccf64cc85045ff626211bb4941b1f95bc373'
                    ]
                ], // 默认封面图片
                'maxPicCnt' => 5, // 最多支持几张默认封面图片
                'defaultTitle' => ['#学生名#', '的', '#章节名称#', '的课堂报告'],
                'cardVariable' => ['学生名', '章节名称'] // 卡片支持的变量名
            ],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_DEER_PROGRAM_REPORT_YOUKETANG => [
            'name'                  => '群发课堂报告（编程进校）',
            'subtitle'              => '群发课堂报告（编程进校）',
            'subtitleDesc'          => '',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_SINGLE,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_DEER_PROGRAM_REPORT_YOUKETANG,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [
                self::IS_SEND_LESSON,
                self::DEER_HOMEWORK_LEVEL,
                self::DEER_PROGRAM_HOMEWORK_STATUS,
            ],
            'hasSendCard' => [  // 发送卡片能力
                'isShow' => true, // 是否展示
                'sendCardField' => '#小鹿编程课堂报告#', // 发送卡片链接对应字段
                'defaultPic' => [
                    [
                        'url' => 'https://charge.zuoyebang.cc/fudao_11fac43a2dd1f229a4caef2140ac8ea2.jpg?authorization=bce-auth-v1%2F80054b2251d24cb98eda994c93426d7d%2F2024-03-21T02%3A52%3A50Z%2F-1%2Fhost%2Fe7e50d85c46d31e335a0f8d63a14f05cdf740e7d611e3945ffa8be41d2893a0c'
                    ],
                    [
                        'url' => 'https://charge.zuoyebang.cc/fudao_7e490dd0b1a3d30a9e16c39067f91f55.jpg?authorization=bce-auth-v1%2F80054b2251d24cb98eda994c93426d7d%2F2024-03-21T03%3A01%3A10Z%2F-1%2Fhost%2Ff694b21efab30ac74acde93d4aaa5df8292bc990d11f169afa0237ca1cbf77bc'
                    ],
                    [
                        'url' => 'https://charge.zuoyebang.cc/fudao_7d57561b37e359796482161889e8331f.jpg?authorization=bce-auth-v1%2F80054b2251d24cb98eda994c93426d7d%2F2024-03-21T03%3A01%3A52Z%2F-1%2Fhost%2F79e5670b3d33d5c43e0e4d85e974ccf64cc85045ff626211bb4941b1f95bc373'
                    ]
                ], // 默认封面图片
                'maxPicCnt' => 5, // 最多支持几张默认封面图片
                'defaultTitle' => ['#学生名#', '的', '#章节名称#', '的课堂报告'],
                'cardVariable' => ['学生名', '章节名称'] // 卡片支持的变量名
            ],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_STAGE_RESULT_FEEDBACK_CARD => [
            'name'                  => '阶段测结果反馈（辅导）',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_SINGLE,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_STAGE_RESULT_FEEDBACK_CARD,
            'stuType'               => '已提交阶段测',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [
                self::IS_SEND_LESSON,
                self::IS_STAGE_REPORT_COMMIT_LESSON,
                self::STAGE_REPORT_SCORE_LESSON,
            ],
            'urlCaptureSrcMap' => [
                '阶段测报告图片' => ['https://img.zuoyebang.cc/zyb_15ece1ea1190f8144e38436a8ee9148b.png']
            ],
            'hasSendCardMap' => [
                '阶段测报告卡片' => [
                    'isShow' => true,
                    'sendCardField' => '#阶段测报告链接#',
                    'defaultPic' => [
                        ["url"=>"https://img.zuoyebang.cc/zyb_d050f4cda11335e5e4b97b1dc03e6709.png","iconType"=>4],
                        ["url"=>"https://img.zuoyebang.cc/zyb_7c1382c118a82d8e5ba6cc9918f091ab.png","iconType"=>3],
//                        ["url"=>"https://img.zuoyebang.cc/zyb_08e443d876b668c16b006028848e176c.png","iconType"=>2],
                        ["url"=>"https://img.zuoyebang.cc/zyb_e8e9fd911f851fd27943e900e9e2a951.png","iconType"=>8],
                        ["url"=>"https://img.zuoyebang.cc/zyb_8522a2787f5824a8591e95975d5ee499.png","iconType"=>2],  // 鲸鱼爱学
                        ["url"=>"https://img.zuoyebang.cc/zyb_a7a61fa61cb81a8c32afb1fb3e42b245.png","iconType"=>11],  // 帮帮好课
                    ],
                    'maxPicCnt' => 5,
                    'defaultTitle' => ['请查收', '#学生名#', '的阶段测自评'],
                    'cardVariable' => ["学生名", "课程名称", "学科"],
                    'introductionVariable' => ["学生名", "课程名称", "学科"],
                    'defaultIntroduction' => ['点击查看孩子阶段测自评分析及近期表现情况'],
                ]
            ]
//            'hasSendCard' => [  // 发送卡片能力
//                'isShow' => true, // 是否展示
//                'sendCardField' => '#阶段测报告链接#', // 发送卡片链接对应字段
//                'defaultPic' => [
//                    [
//                        'url' => 'https://charge.zuoyebang.cc/fudao_11fac43a2dd1f229a4caef2140ac8ea2.jpg?authorization=bce-auth-v1%2F80054b2251d24cb98eda994c93426d7d%2F2024-03-21T02%3A52%3A50Z%2F-1%2Fhost%2Fe7e50d85c46d31e335a0f8d63a14f05cdf740e7d611e3945ffa8be41d2893a0c'
//                    ],
//                    [
//                        'url' => 'https://charge.zuoyebang.cc/fudao_7e490dd0b1a3d30a9e16c39067f91f55.jpg?authorization=bce-auth-v1%2F80054b2251d24cb98eda994c93426d7d%2F2024-03-21T03%3A01%3A10Z%2F-1%2Fhost%2Ff694b21efab30ac74acde93d4aaa5df8292bc990d11f169afa0237ca1cbf77bc'
//                    ],
//                    [
//                        'url' => 'https://charge.zuoyebang.cc/fudao_7d57561b37e359796482161889e8331f.jpg?authorization=bce-auth-v1%2F80054b2251d24cb98eda994c93426d7d%2F2024-03-21T03%3A01%3A52Z%2F-1%2Fhost%2F79e5670b3d33d5c43e0e4d85e974ccf64cc85045ff626211bb4941b1f95bc373'
//                    ]
//                ], // 默认封面图片
//                'maxPicCnt' => 5, // 最多支持几张默认封面图片
//                'defaultTitle' => ['请查收', '#学生名#', '的阶段测自评'],
//                'cardVariable' => ['学生名', '课程名称','章节名称'], // 卡片支持的变量名
//                'introductionVariable' => ['学生名', '课程名称','章节名称'], //描述支持的变量
//                'defaultIntroduction' => ['点击查看孩子阶段测自评分析及近期表现情况'] //描述支持的变量
//            ],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_CONTRACT_SEMESTER_REPORT_SENIOR => [
            'name'                  => '学期报告反馈（高中）',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_SINGLE,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_CONTRACT_SEMESTER_REPORT_SENIOR,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [
                self::SEMESTER_REPORT_SENIOR_GENERATE_STATUS,
                self::SEMESTER_REPORT_SENIOR_SEND_STATUS,
                self::SEMESTER_REPORT_SENIOR_LOOK_STATUS,
                self::SEMESTER_REPORT_SENIOR_COMPLETE_LOOK_STATUS
            ],
            'hasSendCard' => [  // 发送卡片能力
                'isShow' => true, // 是否展示
                'sendCardField' => '#学期报告链接#', // 发送卡片链接对应字段
                'defaultPic' => [
                    ["url"=>"https://img.zuoyebang.cc/zyb_d050f4cda11335e5e4b97b1dc03e6709.png","iconType"=>4],
                    ["url"=>"https://img.zuoyebang.cc/zyb_7c1382c118a82d8e5ba6cc9918f091ab.png","iconType"=>3],
//                    ["url"=>"https://img.zuoyebang.cc/zyb_08e443d876b668c16b006028848e176c.png","iconType"=>2],
                    ["url"=>"https://img.zuoyebang.cc/zyb_0f04f1e5df9208e82a012b4b868d490f.png","iconType"=>1],
                ], // 默认封面图片
                'maxPicCnt' => 5, // 最多支持几张默认封面图片
                'defaultTitle' => ['#学生名#', '请查收高中','#学科#', '的学期报告'],
                'cardVariable' => ['学生名', '学科', '课程名称'], // 卡片支持的变量名
                'introductionVariable' => [], //描述支持的变量
                'defaultIntroduction' => ['点击这里, 快速查看'] //描述支持的变量
            ],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_RJYL_REPORT => [
            'name'                  => '群发日积月累报告',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_SINGLE,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_RJYL_REPORT,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [
            ],
            'hasSendCard' => [  // 发送卡片能力
                'isShow' => true, // 是否展示
                'sendCardField' => '#日积月累报告链接#', // 发送卡片链接对应字段
                'defaultPic' => [
                    [
                        'url' => 'https://charge.zuoyebang.cc/fudao_11fac43a2dd1f229a4caef2140ac8ea2.jpg?authorization=bce-auth-v1%2F80054b2251d24cb98eda994c93426d7d%2F2024-03-21T02%3A52%3A50Z%2F-1%2Fhost%2Fe7e50d85c46d31e335a0f8d63a14f05cdf740e7d611e3945ffa8be41d2893a0c'
                    ],
                    [
                        'url' => 'https://charge.zuoyebang.cc/fudao_7e490dd0b1a3d30a9e16c39067f91f55.jpg?authorization=bce-auth-v1%2F80054b2251d24cb98eda994c93426d7d%2F2024-03-21T03%3A01%3A10Z%2F-1%2Fhost%2Ff694b21efab30ac74acde93d4aaa5df8292bc990d11f169afa0237ca1cbf77bc'
                    ],
                    [
                        'url' => 'https://charge.zuoyebang.cc/fudao_7d57561b37e359796482161889e8331f.jpg?authorization=bce-auth-v1%2F80054b2251d24cb98eda994c93426d7d%2F2024-03-21T03%3A01%3A52Z%2F-1%2Fhost%2F79e5670b3d33d5c43e0e4d85e974ccf64cc85045ff626211bb4941b1f95bc373'
                    ]
                ], // 默认封面图片
                'maxPicCnt' => 5, // 最多支持几张默认封面图片
                'defaultTitle' => ['请查收', '#学生名#', '的日积月累报告'],
                'cardVariable' => ['学生名', '课程名称','章节名称'], // 卡片支持的变量名
                'introductionVariable' => ['学生名', '课程名称','章节名称'], //描述支持的变量
                'defaultIntroduction' => ['点击查看孩子日积月累报告及近期表现情况'] //描述支持的变量
            ],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_EDU_PROBE_PDF => [
            'name'                  => '群发学情报告（高中）',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_SINGLE,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_EDU_PROBE_PDF,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [
                self::IS_SEND,  // 是否已发送学情报告
            ],
            'hasSendCard' => [  // 发送卡片能力
                'isShow' => true, // 是否展示
                'sendCardField' => '#学情报告链接#', // 发送卡片链接对应字段
                'defaultPic' => [
                    ["url"=>"https://img.zuoyebang.cc/zyb_d050f4cda11335e5e4b97b1dc03e6709.png","iconType"=>4],
                    ["url"=>"https://img.zuoyebang.cc/zyb_7c1382c118a82d8e5ba6cc9918f091ab.png","iconType"=>3],
//                    ["url"=>"https://img.zuoyebang.cc/zyb_08e443d876b668c16b006028848e176c.png","iconType"=>2],
                    ["url"=>"https://img.zuoyebang.cc/zyb_e8e9fd911f851fd27943e900e9e2a951.png","iconType"=>8],
                ], // 默认封面图片
                'maxPicCnt' => 5, // 最多支持几张默认封面图片
                'defaultTitle' => ['#学生名#', '请查收你的学情测评报告'],
                'cardVariable' => ['学生名', '辅导老师真名'], // 卡片支持的变量名
                'introductionVariable' => ['学生名', '辅导老师真名'], //描述支持的变量
                'defaultIntroduction' => ['点击这里，快速查看'] //描述支持的变量
            ],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_SUBJECT_DIAG_REPORT => [
            'name'                  => '群发学科诊断报告（初中）',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_SINGLE,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_SUBJECT_DIAG_REPORT,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [
                self::IS_SEND,  // 是否已发送学情报告
            ],
            'hasSendCard' => [  // 发送卡片能力
                'isShow' => true, // 是否展示
                'sendCardField' => '#学科诊断报告链接#', // 发送卡片链接对应字段
                'defaultPic' => [
                    ["url"=>"https://img.zuoyebang.cc/zyb_d050f4cda11335e5e4b97b1dc03e6709.png","iconType"=>4],
                    ["url"=>"https://img.zuoyebang.cc/zyb_7c1382c118a82d8e5ba6cc9918f091ab.png","iconType"=>3],
//                    ["url"=>"https://img.zuoyebang.cc/zyb_08e443d876b668c16b006028848e176c.png","iconType"=>2],
                    ["url"=>"https://img.zuoyebang.cc/zyb_e8e9fd911f851fd27943e900e9e2a951.png","iconType"=>8],
                ], // 默认封面图片
                'maxPicCnt' => 5, // 最多支持几张默认封面图片
                'defaultTitle' => ['#学生名#', '请查收你的学科诊断规划报告'],
                'cardVariable' => ['学生名', '辅导老师真名'], // 卡片支持的变量名
                'introductionVariable' => ['学生名', '辅导老师真名'], //描述支持的变量
                'defaultIntroduction' => ['点击这里，查看报告'] //描述支持的变量
            ],
        ],

        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_PERSONAL_LEARN_FEEDBACK => [
            'name'                  => '个性化学情反馈',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_SINGLE,
            'filterType'            => self::FILTER_GROUP,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_PERSONAL_LEARN_FEEDBACK,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [
                self::IS_SEND,
            ],
            'sceneContext' => [
                [
                    'label' => '阶段测学情',
                    'type' => 'select',
                    'isMulti' => false,
                    'dimension' => 'course',
                    'key' => 'exam9',
                    'required' => true,
                    'items' => [],
                ],
                [
                    'label' => '课中学情',
                    'type' => 'select',
                    'isMulti' => true,
                    'dimension' => 'course',
                    'key' => 'lesson',
                    'required' => true,
                    'items' => [],
                ]
            ],
        ],

        //群发错题任务
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_EXERCISE_NOTE_TASK => [
            'name'                  => '群发错题任务',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_MULTI,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_EXERCISE_NOTE_TASK,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [],
            'hasSendCard' => [  // 发送卡片能力
                'isShow' => true, // 是否展示
                'canHide' => true, //支持隐藏
                'sendCardField' => '', // 发送卡片链接对应字段
                'defaultPic' => [
                    ["url"=>"https://img.zuoyebang.cc/zyb_d050f4cda11335e5e4b97b1dc03e6709.png","iconType"=>4],
                    ["url"=>"https://img.zuoyebang.cc/zyb_7c1382c118a82d8e5ba6cc9918f091ab.png","iconType"=>3],
//                    ["url"=>"https://img.zuoyebang.cc/zyb_08e443d876b668c16b006028848e176c.png","iconType"=>2],
                    ["url"=>"https://img.zuoyebang.cc/zyb_e8e9fd911f851fd27943e900e9e2a951.png","iconType"=>8],
                ], // 默认封面图片
                'maxPicCnt' => 5, // 最多支持几张默认封面图片
                'defaultTitle' => ['#学生名#', '请查收你的专属错题任务'], //标题
                'cardVariable' => ['学生名','课程名称','学科'], // 卡片支持的变量名
                'introductionVariable' => ['学生名','课程名称','学科'], //描述支持的变量
                'defaultIntroduction' => ['点击链接开始作答，快来重做错题吧'] //副标题
            ],
        ],

        //群发重做报告
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_EXERCISE_NOTE_CLEAN_REPORT => [
            'name'                  => '群发重做报告',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_MULTI,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_EXERCISE_NOTE_CLEAN_REPORT,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [],
            'hasSendCard' => [  // 发送卡片能力
                'isShow' => true, // 是否展示
                'canHide' => true, //支持隐藏
                'sendCardField' => '', // 发送卡片链接对应字段
                'defaultPic' => [
                    ["url"=>"https://img.zuoyebang.cc/zyb_d050f4cda11335e5e4b97b1dc03e6709.png","iconType"=>4],
                    ["url"=>"https://img.zuoyebang.cc/zyb_7c1382c118a82d8e5ba6cc9918f091ab.png","iconType"=>3],
//                    ["url"=>"https://img.zuoyebang.cc/zyb_08e443d876b668c16b006028848e176c.png","iconType"=>2],
                    ["url"=>"https://img.zuoyebang.cc/zyb_e8e9fd911f851fd27943e900e9e2a951.png","iconType"=>8],
                ], // 默认封面图片
                'maxPicCnt' => 5, // 最多支持几张默认封面图片
                'defaultTitle' => ['#学生名#', '请查收错题重做报告'], //标题
                'cardVariable' => ['学生名','课程名称','学科'], // 卡片支持的变量名
                'introductionVariable' => ['学生名','课程名称','学科'], //描述支持的变量
                'defaultIntroduction' => ['点击链接查看具体作答情况'] //副标题
            ],
        ],
        //群发重做报告
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_ASSISTANT_COURSE_QUICK_TASK => [
            'name'                  => '批量创建小班课',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_MULTI,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_ASSISTANT_COURSE_QUICK_TASK,
            'stuType'               => '',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_PRONUNCIATION_REPORT => [
            'name'                  => '群发纠音报告',
            'maxLength'             => self::MAX_LENGTH_LONG,
            'sendMode'              => self::SEND_SINGLE,
            'filterType'            => self::FILTER_CLASS,
            'sendType'              => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_PRONUNCIATION_REPORT,
            'stuType'               => '群发纠音报告',
            'fileTitle'             => '',
            'fileName'              => '',
            'fileSubtitle'          => '',
            'imgSrc'                => '',
            'imgTransfer'           => false,
            'loadRenderImg'         => false,
            'loadWrongQuestionPdf'  => false,
            'sceneFilter'           => [
                self::IS_SEND_LESSON,
                self::IS_PRONUNCIATION_REPORT_COMMIT_LESSON,
                self::IS_PRONUNCIATION_REPORT_LEVEL_LESSON,
            ],
            'urlCaptureSrcMap' => [
                '纠音报告图片' => ['https://img.zuoyebang.cc/zyb_7621511a87cd66236dc578c64f5118dc.png']
            ],
            'hasSendCardMap' => [
                '纠音报告卡片' => [
                    'isShow' => true,
                    'sendCardField' => '#纠音报告链接#',
                    'defaultPic' => [
                        ["url"=>"https://img.zuoyebang.cc/zyb_7c1382c118a82d8e5ba6cc9918f091ab.png","iconType"=>3],
                        ["url"=>"https://img.zuoyebang.cc/zyb_d050f4cda11335e5e4b97b1dc03e6709.png","iconType"=>4],
//                        ["url"=>"https://img.zuoyebang.cc/zyb_08e443d876b668c16b006028848e176c.png","iconType"=>2],
                        ["url"=>"https://img.zuoyebang.cc/zyb_e8e9fd911f851fd27943e900e9e2a951.png","iconType"=>8],
                    ],
                    'maxPicCnt' => 5,
                    'defaultTitle' => ['请查收', '#学生名#', '的纠音报告'],
                    'cardVariable' => ["学生名", "课程名称", "学科"],
                    'introductionVariable' => ["学生名", "课程名称", "学科"],
                    'defaultIntroduction' => ['点击这里，快速查看'],
                ]
            ]
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_LEARN_REPORT_CAMBRIDGE_ENGLISH => [
            'name' => '群发课堂报告（剑桥英语)',
            'subtitle' => '短信模版',
            'subtitleDesc' => '',
            'maxLength' => self::MAX_LENGTH_LONG,
            'sendMode' => self::SEND_SMS,
            'filterType' => self::FILTER_CLASS,
            'sendType' => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_LEARN_REPORT_CAMBRIDGE_ENGLISH,
            'stuType' => '',
            'fileTitle' => '',
            'fileName' => '',
            'fileSubtitle' => '',
            'imgSrc' => '',
            'imgTransfer' => false,
            'loadRenderImg' => false,
            'loadWrongQuestionPdf' => false,
            'sceneFilter' => [
                self::IS_SEND_LESSON,
            ],
            'urlCaptureSrcMap' => [
                '课堂报告图片' => ['https://img.zuoyebang.cc/zyb_86217c1b2c54b628b7ff9b7e8548202b.png']
            ],
            'hasSendCardMap' => [
                '课堂报告卡片' => [
                    'isShow' => true,
                    'sendCardField' => '#课堂报告链接#',
                    'defaultPic' => [
                        ["url" => "https://img.zuoyebang.cc/zyb_d050f4cda11335e5e4b97b1dc03e6709.png", "iconType" => 4],
                        ["url" => "https://img.zuoyebang.cc/zyb_7c1382c118a82d8e5ba6cc9918f091ab.png", "iconType" => 3],
//                        ["url" => "https://img.zuoyebang.cc/zyb_08e443d876b668c16b006028848e176c.png", "iconType" => 2],
                        ["url" => "https://img.zuoyebang.cc/zyb_e8e9fd911f851fd27943e900e9e2a951.png", "iconType" => 8],
                    ],
                    'maxPicCnt' => 5,
                    'defaultTitle' => ['#学生名#', '请查收你的课堂报告'],
                    'cardVariable' => ["学生名", "课程名称", "学科"],
                    'introductionVariable' => ["学生名", "课程名称", "学科"],
                    'defaultIntroduction' => ["点击这里，快速查看"],
                ]
            ]
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_PRONUNCIATION_REPORT_GROUP => [
            'name' => '群发个性化纠音报告',
            'subtitle' => '短信模版',
            'subtitleDesc' => '',
            'maxLength' => self::MAX_LENGTH_LONG,
            'sendMode' => self::SEND_SMS,
            'filterType' => self::FILTER_GROUP,
            'sendType' => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_PRONUNCIATION_REPORT_GROUP,
            'stuType' => '',
            'fileTitle' => '',
            'fileName' => '',
            'fileSubtitle' => '',
            'imgSrc' => '',
            'imgTransfer' => false,
            'loadRenderImg' => false,
            'loadWrongQuestionPdf' => false,
            'sceneFilter' => [
                self::IS_SEND_LESSON,
            ],
            'urlCaptureSrcMap' => [
                '纠音报告图片' => ['https://img.zuoyebang.cc/zyb_7621511a87cd66236dc578c64f5118dc.png']
            ],
            'hasSendCardMap' => [
                '纠音报告卡片' => [
                    'isShow' => true,
                    'sendCardField' => '#纠音报告链接#',
                    'defaultPic' => [
                        ["url" => "https://img.zuoyebang.cc/zyb_7c1382c118a82d8e5ba6cc9918f091ab.png", "iconType" => 3],
                        ["url" => "https://img.zuoyebang.cc/zyb_d050f4cda11335e5e4b97b1dc03e6709.png", "iconType" => 4],
//                        ["url" => "https://img.zuoyebang.cc/zyb_08e443d876b668c16b006028848e176c.png", "iconType" => 2],
                        ["url" => "https://img.zuoyebang.cc/zyb_e8e9fd911f851fd27943e900e9e2a951.png", "iconType" => 8],
                    ],
                    'maxPicCnt' => 5,
                    'defaultTitle' => ['请查收', '#学生名#', '的纠音报告'],
                    'cardVariable' => ["学生名", "课程名称", "学科"],
                    'introductionVariable' => ["学生名", "课程名称", "学科"],
                    'defaultIntroduction' => ['点击这里，快速查看'],
                ]
            ],
            "groupItems" => [
                ["value" => Service_Page_DeskV1_SendMsg_GroupFilter::CUSTOM_GROUP_DEFAULT, "label" => "不分组", "type" => "normal"],
                ["value" => Service_Page_DeskV1_SendMsg_GroupFilter::CUSTOM_GROUP_PRONUNCIATION_LEVEL, "label" => "按星级分组", "type" => "normal"],
                ["value" => Service_Page_DeskV1_SendMsg_GroupFilter::CUSTOM_GROUP_PRONUNCIATION_ANSWER, "label" => "按第%s题作答正误分组", "type" => "custom"]
            ],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_VOICE_REPORT_GROUP => [
            'name' => '群发配音小达人报告',
            'subtitle' => '短信模版',
            'subtitleDesc' => '',
            'maxLength' => self::MAX_LENGTH_LONG,
            'sendMode' => self::SEND_SMS,
            'filterType' => self::FILTER_GROUP,
            'sendType' => Assistant_Ds_WxMessageSendRecord::SEND_TYPE_VOICE_REPORT_GROUP,
            'stuType' => '',
            'fileTitle' => '',
            'fileName' => '',
            'fileSubtitle' => '',
            'imgSrc' => '',
            'imgTransfer' => false,
            'loadRenderImg' => false,
            'loadWrongQuestionPdf' => false,
            'sceneFilter' => [
                self::IS_SEND_LESSON,
            ],
            'hasSendCardMap' => [
                '配音小达人报告卡片' => [
                    'isShow' => true,
                    'sendCardField' => '#配音小达人报告链接#',
                    'defaultPic' => [
                        ["url" => "https://img.zuoyebang.cc/zyb_7c1382c118a82d8e5ba6cc9918f091ab.png", "iconType" => 3],
                        ["url" => "https://img.zuoyebang.cc/zyb_d050f4cda11335e5e4b97b1dc03e6709.png", "iconType" => 4],
//                        ["url" => "https://img.zuoyebang.cc/zyb_08e443d876b668c16b006028848e176c.png", "iconType" => 2],
                        ["url" => "https://img.zuoyebang.cc/zyb_e8e9fd911f851fd27943e900e9e2a951.png", "iconType" => 8],
                    ],
                    'maxPicCnt' => 5,
                    'defaultTitle' => ['请查收', '#学生名#', '的配音报告'],
                    'cardVariable' => ["学生名", "课程名称", "学科"],
                    'introductionVariable' => ["学生名", "课程名称", "学科"],
                    'defaultIntroduction' => ['点击这里，快速查看'],
                ]
            ],
            "groupItems" => [
                ["value" => Service_Page_DeskV1_SendMsg_GroupFilter::CUSTOM_GROUP_DEFAULT, "label" => "不分组", "type" => "normal"],
                ["value" => Service_Page_DeskV1_SendMsg_GroupFilter::CUSTOM_GROUP_PRONUNCIATION_LEVEL, "label" => "按星级分组", "type" => "normal"],
            ]
        ]
    ];



    //Service_Page_Desk_Filter_GetFilterMap
    const SCENE_NEED_KEY = [
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_PREVIEW                => [
            'previewFinishStatus' => 0,
            'groupName'           => null,
            'studentName'         => null,
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_PREATTEND              => [
            'preAttendStatus' => [
                Service_Data_LessonStudent::PRE_ATTEND_OK,
            ],
            'groupName'       => null,
            'studentName'     => null,
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_PLAYBACK               => [
            'watchStatus' => [0, 1],//未生成数据或者未到课未看回放
            'groupName'   => null,
            'studentName' => null,
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_NOTES                  => [
            'groupName'   => null,
            'studentName' => null,
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_HOMEWORK               => [
            'homeworkSubmitStatus' => 0,
            'groupName'            => null,
            'studentName'          => null,
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_SUBMIT_HOMEWORK        => [
            'homeworkCorrectStatus' => AssistantDesk_Config::HOMEWORK_CORRECT_STATUS_TB_SUBMITTED,
            'groupName'             => null,
            'studentName'           => null,
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_SUBMIT_HOMEWORK_LIKE   => [
            'similarHomeworkStatus' => AssistantDesk_Config::EXAM_CORRECT_STATUS_TB_SUBMITTED,
            'groupName'             => null,
            'studentName'           => null,
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_SUBMIT_ORAL_QUESTION   => [
            'oralQuestionFinishStatus' => AssistantDesk_TaskMap::ORALQU_STATUS_UNSUBMIT,
            'groupName'                => null,
            'studentName'              => null,
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_ORAL_QUESTION_FEEDBACK => [
            'oralQuestionFinishStatus' => AssistantDesk_TaskMap::ORALQU_STATUS_SUBMIT,
            'groupName'                => null,
            'studentName'              => null,
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_RJYL                   => [
            'stageFinishStatus' => AssistantDesk_Rjyl::MRYL_SUBMIT_STATUS_2,
            'groupName'         => null,
            'studentName'       => null,
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_MRYL                   => [
            'mryl'        => [
                AssistantDesk_Rjyl::MRYL_SUBMIT_STATUS_1,// todo 未解锁 未解锁的是否需要 @yuming @kexin
                AssistantDesk_Rjyl::MRYL_SUBMIT_STATUS_2
            ],
            'groupName'   => null,
            'studentName' => null,
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_HIGHLIGHT              => [
            'groupName'   => null,
            'studentName' => null,
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_INTRODUCEPOSTER        => [
            'groupName'   => null,
            'studentName' => null,
        ],

        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_SATISFACTION_QUE         => [
            'queStatus'   => 1,
            'groupName'   => null,
            'studentName' => null,
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_ATTEND                   => [
            'preAttendStatus' => [0, 2, 3, 4, 5, 6, 7],
            'groupName'       => null,
            'studentName'     => null,
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_GUANJIA_ATTEND                   => [
            'preAttendStatus' => [0, 2, 3, 4, 5, 6, 7],
            'groupName'       => null,
            'studentName'     => null,
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_7                        => [
            'previewFinishStatus' => 1,
            'groupName'           => null,
            'studentName'         => null,
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_ATTEND_OVERVIEW          => [
            'groupName'   => null,
            'studentName' => null,
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_ATTEND_OVERVIEW_PLAYBACK => [
            'groupName'   => null,
            'studentName' => null,
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_REVISE_HOMEWORK          => [
            'homeworkCorrectStatus' => 5,
            'groupName'             => null,
            'studentName'           => null,
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_REVISE_HOMEWORK_LIKE     => [
            'similarHomeworkStatus' => AssistantDesk_Config::EXAM_CORRECT_STATUS_TB_RESUBMITTED,
            'groupName'             => null,
            'studentName'           => null,
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_16                       => [
            'groupName'      => null,
            'studentName'    => null,
            'interviewLabel' => 0, //未填写家访问卷的
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_CORRECT_FEEDBACK         => [
            'homeworkLevel' => [
                "S",
                "A",
                "B",
                "C",
                "D",
            ],
            'groupName'     => null,
            'studentName'   => null,
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_CORRECT_FEEDBACK_DEER         => [
            'deerProgrammingHomeworkLevelCommon' => [
                "1",
                "2",
                "3",
            ],
            'groupName'     => null,
            'studentName'   => null,
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_CORRECT_FEEDBACK_LIKE    => [
            'similarHomeworkSubmitNum' => [
                1,
                2,
                3,
                4,
                5,
                6,
                7,
                8,
                9,
                10,
            ],
            'similarHomeworkLevel' => [
                "S",
                "A",
                "B",
                "C",
                "D",
            ],
            'groupName'     => null,
            'studentName'   => null,
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_COMPOSITION_REPORT       => [
            'homeworkLevel' => [
                "S",
                "A",
                "B",
                "C",
                "D",
            ],
            'groupName'     => null,
            'studentName'   => null,
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_REVISE_HOMEWORK_COMMON       => [
            'homeworkCorrectStatus' => 5,
            'groupName'             => null,
            'studentName'           => null,
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_COMPOSITION_REPORT_COMMON       => [
            'commonHomeworkLevel' => [
                "S",
                "A",
                "B",
                "C",
                "D",
            ],
            'groupName'     => null,
            'studentName'   => null,
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_SUBMIT_MONTHLY_EXAM      => [
            'groupName'   => null,
            'studentName' => null,
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_CUOTIBEN_JIEXI           => [
            'groupName'   => null,
            'studentName' => null,
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_CUOTIBEN_ZUODA           => [
            'groupName'   => null,
            'studentName' => null,
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_EXERCISE_NOTE_TASK_JIEXI           => [
            'groupName'   => null,
            'studentName' => null,
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_EXERCISE_NOTE_TASK_ZUODA           => [
            'groupName'   => null,
            'studentName' => null,
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_MONTHLYEXAM_REPORT       => [
            'inClassTestFinishStatus' => AssistantDesk_TaskMap::EXAM_RESULT_FINISH,
            'groupName'               => null,
            'studentName'             => null,
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_STAGE_SUBMIT             => [
            'stageTestFinishStatus' => AssistantDesk_TaskMap::EXAM_RESULT_UNFINISH,
            'groupName'             => null,
            'studentName'           => null,
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_STAGE_RESULT_FEEDBACK    => [
            'stageTestFinishStatus' => AssistantDesk_TaskMap::EXAM_RESULT_FINISH,
            'groupName'             => null,
            'studentName'           => null,
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_STAGE_RESULT_FEEDBACK_SENIOR    => [
            'groupName'             => null,
            'studentName'           => null,
        ],

        //lpc
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_PREVIEW_LPC => [
            'lessonPreview.isPreviewed' => 0,
            'groupName'                 => null,
            'studentName'               => null
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_BOTTOM_TEST              => [
            'preCompliTestData.preCompliTestData_*'  => null,
            'preCompliTestData.preCompliTestScore_*' => null,
            'groupName'                              => null,
            'studentName'                            => null
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_BOTTOM_TEST_FUDAO              => [
            'groupName'               => null,
            'studentName'             => null
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_PERIOD_EXAM              => [
            'peroidExamScore.peroidExamStatus' => 0,
            'groupName'                        => null,
            'studentName'                      => null
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_EXERCISE_REPORT          => [
            'strengthPracticeWord.compositionReportIsAlreadySend' => 0,
            'groupName'                                           => null,
            'studentName'                                         => null
        ],

        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_ATTEND_LPC   => [
            'isAttendWord.inclassOnline'          => 0,
            'isAttendWord.inclassFirstAttendTime' => 0,
            'groupName'                           => null,
            'studentName'                         => null
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_PLAYBACK_LPC => [
            'isAttendWord.isAttendStatus'     => 0,
            'isPlaybackWord.isPlaybackStatus' => 0,
            'groupName'                       => null,
            'studentName'                     => null
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_NOTES_LPC    => [
            'groupName'   => null,
            'studentName' => null
        ],

        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_BOTTOM_TEST_REPORT  => [
            'preCompliTestData.preCompliTestData_*' => null,
            'groupName'                             => null,
            'studentName'                           => null
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_BOTTOM_TEST_REPORT_FD => [
            'groupName'                             => null,
            'studentName'                           => null
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_SUBMIT_HOMEWORK_LPC => [
            'strengthPracticeWord.strengthPracticeStatus' => 1,
            'groupName'                                   => null,
            'studentName'                                 => null
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_HIGHLIGHT_LPC       => [
            'attendNumNew.courseReviewUrl' => null,
            'groupName'                    => null,
            'studentName'                  => null
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_SURVEY              => [
            'needSurveyStatus.needStatus' => 0,
            'groupName'                   => null,
            'studentName'                 => null
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_CLASS_REPORT        => [
            "isGenerateLessonReportV2" => 1,
            'groupName'   => null,
            'studentName' => null
        ],
        // 没有加微 没法发微信，除非发名片 一下不用
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_ADD_WX_LPC          => [
            'lpcPhone.weChatType' => 1,
            'groupName'           => null,
            'studentName'         => null
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_SERVICE_ADD_WX_LPC  => [
            'groupName'   => null,
            'studentName' => null
        ],

        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_DEER_ORDER    => [
            'deerCouponSendStatus' => 0,
            'groupName'            => null,
            'studentName'          => null
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_DEER_CONTINUE => [
            'deerIsL1r'   => 0,
            'groupName'   => null,
            'studentName' => null
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_GUANJIA_COMMIT_49 => [
            'classPracticeStatus' => 1,
            'groupName'           => null,
            'studentName'         => null
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_GUANJIA_COMMIT_50 => [
            'caseReflectionStatus' => 1,
            'groupName'            => null,
            'studentName'          => null
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_GUANJIA_COMMIT_51 => [
            'powerUpsStatus' => 1,
            'groupName'      => null,
            'studentName'    => null
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_GUANJIA_COMMIT_52 => [
            'weakSpecialStatus' => 1,
            'groupName'         => null,
            'studentName'       => null
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_GUANJIA_RE_COMMIT_49 => [
            'classPracticeStatus'  => 3,
            'exam49_correct_level' => "neq::1",
            'exam49_amend_num'     => "lt::10",
            'groupName'            => null,
            'studentName'          => null
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_GUANJIA_RE_COMMIT_50 => [
            'caseReflectionStatus' => 3,
            'exam50_correct_level' => "neq::1",
            'exam50_amend_num'     => "lt::10",
            'groupName'            => null,
            'studentName'          => null
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_GUANJIA_RE_COMMIT_51 => [
            'powerUpsStatus'       => 3,
            'exam51_correct_level' => "neq::1",
            'exam51_amend_num'     => "lt::10",
            'groupName'            => null,
            'studentName'          => null
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_GUANJIA_RE_COMMIT_52 => [
            'weakSpecialStatus'    => 3,
            'exam52_correct_level' => "neq::1",
            'exam52_amend_num'     => "lt::10",
            'groupName'            => null,
            'studentName'          => null
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_LEARN_REPORT_FD => [
            "isGenerateLessonReportV2" => 1,
            'groupName'   => null,
            'studentName' => null
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_UNIT_REPORT_DYD => [
            "lessonReportStatus" => 1,
            'groupName'          => null,
            'studentName'        => null
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_SPORT_WEEK_REPORT => [
            'groupName'   => null,
            'studentName' => null
        ],

        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_LESSON_REPORT => [
            'groupName'   => null,
            'studentName' => null,
            'isSubmitLessonWork' => 1
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_COURSE_REPORT => [
            'groupName'   => null,
            'studentName' => null,
            'isSubmitCourseReport' => 1
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_WEEKLY_REPORT => [
            'groupName'   => null,
            'studentName' => null,
        ],
        // AI催完课
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_CONTRACT_AI_OVER_CLASS => [
            'groupName'   => null,
            "is_inclass_teacher_room_content_view_finish_85percent" => 0,
            "isAILessonUnLock" => 1,
        ],
        // 催补课
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_CONTRACT_REMEDIAL_CLASS => [
            // 已解锁，未到课
            'groupName'   => null,
            "is_inclass_teacher_room_content_view_10s" => 0,
            "isAILessonUnLock" => 1,
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_LEARNING_REPORT => [
            'groupName'      => null,
            'studentName'    => null,
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_SUBJECT_DIAG => [
            'groupName'      => null,
            'studentName'    => null,
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_RJYL_REPORT => [
            'groupName'      => null,
            'studentName'    => null,
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_PROBE_REPORT_QUERY => [
            'groupName'      => null,
            'studentName'    => null,
        ],
        Assistant_Ds_WxMessageSendRecord::sEND_TYPE_CORRECT_REPORT => [
            'homeworkCorrectStatus' => 6,
            'studentName' => null,
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_EVALUATE => [
            'groupName'   => null,
            'studentName' => null,
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_COURSE_TIME_TABLE => [
            'groupName'   => null,
            'studentName' => null,
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_QW_ADD_GROUP => [
            'studentName' => null,
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_DEER_PROGRAM_REPORT => [
            'studentName' => null,
            'bc_report_status' => ['2', '3'],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_DEER_PROGRAM_REPORT_YOUKETANG => [
            'studentName' => null,
            'bc_report_status' => ['2', '3'],
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_STAGE_RESULT_FEEDBACK_CARD => [
            'stageTestReportIsComplete' => 1,
            'groupName'             => null,
            'studentName'           => null,
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_CONTRACT_SEMESTER_REPORT_SENIOR => [
            'groupName'             => null,
            'studentName'           => null,
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_EDU_PROBE_PDF => [
            'groupName'             => null,
            'studentName'           => null,
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_SUBJECT_DIAG_REPORT => [
            'groupName'             => null,
            'studentName'           => null,
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_PERSONAL_LEARN_FEEDBACK => [
            'groupName'             => null,
            'studentName'           => null,
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_EXERCISE_NOTE_TASK           => [
            'groupName'   => null,
            'studentName' => null,
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_EXERCISE_NOTE_CLEAN_REPORT           => [
            'groupName'   => null,
            'studentName' => null,
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_LEARN_REPORT_CAMBRIDGE_ENGLISH => [
            'groupName' => null,
            'studentName' => null
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_PRONUNCIATION_REPORT_GROUP => [
            'groupName' => null,
            'studentName' => null
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_VOICE_REPORT_GROUP => [
            'groupName' => null,
            'studentName' => null
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_DEER_CEPING_STAGE1_REPORT => [
            'groupName' => null,
            'studentName' => null
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_DEER_CEPING_STAGE2_REPORT => [
            'groupName' => null,
            'studentName' => null
        ],
    ];

    // sendType => configArr
    //            const BUTTON_TYPE_LPC_SMS_ADD_WX       = 65;//发送用户加微短信 65
//    const BUTTON_TYPE_LPC_SMS_ADD_WX_APPLY = 66;//发送加用户微信申请 66
//    const BUTTON_TYPE_LPC_SMS_ADD_WX_CARD  = 67;//发送用户名片给我 67
//    const BUTTON_TYPE_LPC_SMS_ATTEND       = 68;//催到课短信 68
    const SCENE_NEED_KEY_SMS_LPC = [
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_LPC_SMS_ADD_WX => [
            'lpcPhone.weChatType' => 1,
            'studentName' => null,
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_LPC_SMS_ADD_WX_APPLY => [
            'lpcPhone.weChatType' => 1,
            'studentName' => null,
        ],

        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_LPC_SMS_ADD_WX_CARD => [
            'lpcPhone.weChatType' => 1,
            'studentName' => null,
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_LPC_SMS_ATTEND => [
            'isAttendWord.inclassOnline'          => 0,
            'isAttendWord.inclassFirstAttendTime' => 0,
            'studentName' => null,
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_LPC_SMS_AI_ATTED => [
            "is_inclass_teacher_room_content_view_10s" => 0,
            'studentName' => null,
        ],
    ];
    const SCENE_NEED_KEY_SMS = [
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_LPC_SMS_ADD_WX => [
            'guardianInfoNew' => 0,
            'studentName' => null,
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_LPC_SMS_ADD_WX_APPLY => [
            'guardianInfoNew' => 0,
            'studentName' => null,
        ],

        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_LPC_SMS_ADD_WX_CARD => [
            'guardianInfoNew' => 0,
            'studentName' => null,
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_LPC_SMS_ATTEND => [
            'preAttendStatus' => [0, 2, 3, 4, 5, 6, 7],
            'studentName' => null,
        ],
        Assistant_Ds_WxMessageSendRecord::SEND_TYPE_LPC_SMS_AI_ATTED => [
            "is_inclass_teacher_room_content_view_10s" => 0,
            'studentName' => null,
        ],
    ];

    // 课程维度是否发送
    const IS_SEND = [
        'label' => '',
        'type'  => 'checkbox',
        'isMulti' => true,
        'dimension' => self::DIMENSION_TYPE_COURSE,
        'key' => 'wxSendHistoryIsSend',
        'default' => [],
        'items' => [],

    ];

    // 自定义标签
    const CUSTOM_TAG = [
        'label' => '自定义标签',
        'type'  => 'checkbox',
        'isMulti' => true,
        'dimension' => self::DIMENSION_TYPE_SCENE,
        'key' => 'customTagNew',
        'default' => [],
        'items' => [],
    ];

    // 反选自定义标签
    const REVERSE_CUSTOM_TAG = [
        'label' => '反选自定义标签',
        'type'  => 'checkbox',
        'isMulti' => true,
        'dimension' => self::DIMENSION_TYPE_SCENE,
        'key' => 'customTagNewConvertSelect',
        'default' => [],
        'items' => [],
    ];

    // 运营标签
    const OPERATION_TAG = [
        'label' => '运营标签',
        'type'  => 'checkbox',
        'isMulti' => true,
        'dimension' => self::DIMENSION_TYPE_SCENE,
        'key' => 'operationTagNew',
        'default' => [],
        'items' => [],
    ];

    // 反选运营标签
    const REVERSE_OPERATION_TAG = [
        'label' => '反选运营标签',
        'type'  => 'checkbox',
        'isMulti' => true,
        'dimension' => self::DIMENSION_TYPE_SCENE,
        'key' => 'operationTagNewConvertSelect',
        'default' => [],
        'items' => [],
    ];

    // 课程维度是否发送
    const IS_SUBMIT_EVALUATE = [
        'label' => '',
        'type'  => 'checkbox',
        'isMulti' => true,
        'dimension' => self::DIMENSION_TYPE_COURSE,
        'key' => 'evaluateCommitStatusFilter',
        'default' => [],
        'items' => [],

    ];

    // lesson维度
    const IS_SEND_LESSON = [
        'label' => '',
        'type'  => 'checkbox',
        'isMulti' => true,
        'dimension' => self::DIMENSION_TYPE_LESSON,
        'key' => 'wxSendHistoryLessonIsSend',
        'default' => [],
        'items' => [],

    ];

    // lesson维度 批改场景
    const IS_SEND_LESSON_PG = [
        'label' => '',
        'type'  => 'checkbox',
        'isMulti' => true,
        'dimension' => self::DIMENSION_TYPE_LESSON,
        'key' => 'wxSendHistoryLessonIsSendPG',
        'default' => [],
        'items' => [],

    ];

    const IS_STAGE_REPORT_COMPLETE_LESSON_SENIOR = [
        'label' => '',
        'type'  => 'select',
        'isMulti' => false,
        'dimension' => self::DIMENSION_TYPE_LESSON,
        'key' => 'stageTestReportIsComplete',
        'default' => 1,
        'items' => [],

    ];

    //阶段测报告是否生成
    const IS_STAGE_REPORT_COMPLETE_LESSON = [
        'label' => '',
        'type'  => 'select',
        'isMulti' => false,
        'dimension' => self::DIMENSION_TYPE_LESSON,
        'key' => 'stageTestReportIsComplete',
        'default' => 1,
        'items' => [],

    ];
    //阶段测报告是否提交
    const IS_STAGE_REPORT_COMMIT_LESSON = [
        'label' => '',
        'type'  => 'select',
        'isMulti' => false,
        'dimension' => self::DIMENSION_TYPE_LESSON,
        'key' => 'stageTestCommitStatus',
        'default' => [],
        'items' => [],

    ];

    //纠音报告是否提交
    const IS_PRONUNCIATION_REPORT_COMMIT_LESSON = [
        'label' => '',
        'type'  => 'select',
        'isMulti' => false,
        'dimension' => self::DIMENSION_TYPE_LESSON,
        'key' => 'lpcPronunciationReportIsSubmit',
        'default' => [],
        'items' => [],

    ];

    //纠音报告星级
    const IS_PRONUNCIATION_REPORT_LEVEL_LESSON = [
        'label' => '',
        'type'  => 'select',
        'isMulti' => true,
        'dimension' => self::DIMENSION_TYPE_LESSON,
        'key' => 'lpcPronunciationReportLevel',
        'default' => [],
        'items' => [],

    ];

    //章节观看数量
    const WATCH_LESSON_CNT = [
        'label'     => '',
        'type'      => 'checkbox',
        'isMulti'   => true,
        'dimension' => self::DIMENSION_TYPE_COURSE,
        'key'       => 'watchLessonCnt',
        'default'   => [],
        'items'     => [],

    ];

    //阶段测分数段
    const STAGE_REPORT_SCORE_LESSON = [
        'label' => '',
        'type'  => 'range',
        'isMulti' => true,
        'dimension' => self::DIMENSION_TYPE_LESSON,
        'key' => 'stageTestScoreLimit',
        'default' => [],
        'items' => [],

    ];

    // 阶段测是否已读
    const STAGE_REPORT_IS_READ = [
        'label' => '',
        'type'  => 'select',
        'isMulti' => true,
        'dimension' => self::DIMENSION_TYPE_LESSON,
        'key' => 'isLessonReadStageReport',
        'default' => [],
        'items' => [],
    ];

    const CORRECT_TIMES = [
        'label' => '批改次数反馈',   //筛选名称
        'type'  => 'radio',           //筛选类型
        'isMulti' => false,
        'dimension' => self::DIMENSION_TYPE_LESSON,
        'key' => 'correctTimes.correctTimes',
        'default' => '',
        'items' => [
        ],
    ];

    const IS_L2R_STATUS = [
        'label' => '隔季二级续报状态',
        'type'  => 'checkbox',
        'isMulti' => true,
        'dimension' => self::DIMENSION_TYPE_COURSE,
        'key' => 'l2rSpaceSeasonStatus',
        'default' => [],
        'items' => [],
    ];

    const IS_L2R = [
        'label' => '',
        'type'  => 'checkbox',
        'isMulti' => true,
        'dimension' => self::DIMENSION_TYPE_COURSE,
        'key' => 'continueStatus',
        'default' => [],
        'items' => [],

    ];
    const QUE_STATUS = [
        'label' => '',
        'type'  => 'checkbox',
        'isMulti' => true,
        'dimension' => self::DIMENSION_TYPE_COURSE,
        'key' => 'queStatus',
        'default' => [],
        'items' => [],

    ];

    //仅选择直播未到课学员
    const ATTEND_LIVE_STATUS = [
        'label' => '',
        'type'  => 'checkbox',
        'isMulti' => true,
        'dimension' => self::DIMENSION_TYPE_COURSE,
        'key' => 'watchStatus',
        'default' => [],
        'items' => [],

    ];

    //仅选择直播未到课学员
    const WATCH_ONCOURSE_STATUS = [
        'label' => '',
        'type'  => 'radio',
        'isMulti' => false,
        'dimension' => self::DIMENSION_TYPE_LESSON,
        'key' => 'watchOnCourseStatus',
        'default' => [],
        'items' => [],

    ];

    const ATTEND_FINISH_STATUS = [
        'label' => '',
        'type'  => 'radio',
        'isMulti' => false,
        'dimension' => self::DIMENSION_TYPE_LESSON,
        'key' => 'attendFinishStatus',
        'default' => [],
        'items' => [],

    ];
    const ATTEND_FINISH_STATUS_JUNIOR = [
        'label' => '',
        'type'  => 'radio',
        'isMulti' => false,
        'dimension' => self::DIMENSION_TYPE_LESSON,
        'key' => 'is_inclass_teacher_room_view_finish_total_playback_three_five_v1',
        'default' => [],
        'items' => [],

    ];
    const ATTEND_FINISH_STATUS_SENIOR = [
        'label' => '',
        'type'  => 'radio',
        'isMulti' => false,
        'dimension' => self::DIMENSION_TYPE_LESSON,
        'key' => 'lessonThreeQuartersFinishedTheCourse',
        'default' => [],
        'items' => [],
    ];

    const WATCH_ONCOURSE_ATTEND_STATUS = [
        'label' => '',
        'type'  => 'radio',
        'isMulti' => false,
        'dimension' => self::DIMENSION_TYPE_LESSON,
        'key' => 'is_inclass_teacher_room_view_attend',
        'default' => [],
        'items' => [],

    ];

    //common lu直播完课状态（60%）
    const COMMON_PLAYBACK_THREE_FIVE_V1 = [
        'label' => '',
        'type'  => 'radio',
        'isMulti' => false,
        'dimension' => self::DIMENSION_TYPE_COURSE,
        'key' => 'common_is_inclass_teacher_room_view_finish_total_playback_three_five_v1',
        'default' => [],
        'items' => [],

    ];
    //common lu直播到课状态(通过时长判断，已到课超30分钟，已参课不足30分钟，未到课）
    const COMMON_ATTEND_STATUS_BY_DURATION = [
        'label' => '',
        'type'  => 'checkbox',
        'isMulti' => true,
        'dimension' => self::DIMENSION_TYPE_COURSE,
        'key' => 'attendStatusByDuration',
        'default' => [],
        'items' => [],

    ];

    const LEARNING_REPORT = [
        'label' => '场景下群发是否发送',
        'type'  => 'checkbox',
        'isMulti' => true,
        'dimension' => self::DIMENSION_TYPE_COURSE,
        'key' => 'eduProbePdfStautsfilter',
        'default' => [],
        'items' => [
        ],
    ];

    // 是否入群
    const IS_DEVICE_DATA_GROUP_BIND_STUDENT = [
        'label' => '是否入群',
        'type'  => 'checkbox',
        'isMulti' => true,
        'dimension' => self::DIMENSION_TYPE_COURSE,
        'key' => 'isGroupBindStudentCourse',
        'default' => [0],
        'items' => [],
    ];

    const SEMESTER_REPORT_SENIOR_GENERATE_STATUS = [
        'label' => '学期报告是否生成',
        'type' => 'checkbox',
        'isMulti' => true,
        'dimension' => self::DIMENSION_TYPE_COURSE,
        'key' => 'semesterReportSeniorGenStatusFilter',
        'default' => [1],
        'items' => [],
    ];

    const SEMESTER_REPORT_SENIOR_SEND_STATUS = [
        'label' => '学期报告是否发送',
        'type' => 'checkbox',
        'isMulti' => true,
        'dimension' => self::DIMENSION_TYPE_COURSE,
        'key' => 'semesterReportSeniorSendStatusFilter',
        'default' => [0],
        'items' => [],
    ];

    const SEMESTER_REPORT_SENIOR_LOOK_STATUS = [
        'label' => '学期报告查看',
        'type' => 'checkbox',
        'isMulti' => true,
        'dimension' => self::DIMENSION_TYPE_COURSE,
        'key' => 'semesterReportSeniorLookStatusFilter',
        'default' => [],
        'items' => [],
    ];

    const SEMESTER_REPORT_SENIOR_COMPLETE_LOOK_STATUS = [
        'label' => '学期报告完整查看',
        'type' => 'checkbox',
        'isMulti' => true,
        'dimension' => self::DIMENSION_TYPE_COURSE,
        'key' => 'semesterReportSeniorCompleteLookStatusFilter',
        'default' => [],
        'items' => [],
    ];

    const FILTER_PROBE_PDF_DONE_SENIOR = [
        'label' => '学情报告是否生成',
        'type' => 'checkbox',
        'isMulti' => true,
        'dimension' => self::DIMENSION_TYPE_COURSE,
        'key' => 'isEduProbePdfCreated',
        'default' => [],
        'items' => [],
    ];

    const FILTER_PROBE_PDF_DONE_JUNIOR = [
        'label' => '学科诊断报告是否生成',
        'type' => 'checkbox',
        'isMulti' => true,
        'dimension' => self::DIMENSION_TYPE_COURSE,
        'key' => 'eduProbeStatusJuniorCurrent',
        'default' => [],
        'items' => [],
    ];

    //作文批改次数反馈
    //学员类型
    const USER_TYPE = [
        'label' => '学员类型',
        'type'  => 'checkbox',
        'isMulti' => true,
        'dimension' => self::DIMENSION_TYPE_COURSE,
        'key' => 'studentInfoNew',
        'default' => [],
        'items' => [

        ],

    ];
    const INTERACTION_SUBMIT_STATUS=[
        'label' => '互动题提交情况',
        'type'  => 'checkbox',
        'isMulti' => true,
        'dimension' => self::DIMENSION_TYPE_LESSON,
        'key' => 'interactionSubmitStatus',
        'default' => [],
        'items' => [
        ],
    ];

    const INTERACTION_RIGHT_STATUS=[
        'label' => '互动题答题情况',
        'type'  => 'checkbox',
        'isMulti' => true,
        'dimension' => self::DIMENSION_TYPE_LESSON,
        'key' => 'interactionRightStatus',
        'default' => [],
        'items' => [
        ],
    ];

    const INTERACTION_SUBMIT_RATE=[
        'label' => '观看互动题参与率',
        'type'  => 'checkbox',
        'isMulti' => true,
        'dimension' => self::DIMENSION_TYPE_LESSON,
        'key' => 'interactionParticipateRateFilter',
        'default' => [],
        'items' => [
        ],
    ];

    const INTERACTION_RIGHT_RATE=[
        'label' => '观看互动题正确率',
        'type'  => 'checkbox',
        'isMulti' => true,
        'dimension' => self::DIMENSION_TYPE_LESSON,
        'key' => 'interactionRightRateFilter',
        'default' => [],
        'items' => [
        ],
    ];

    const ATTEND_OPEN_MOUTH_CNT = [
        'label' => '课中开口次数',
        'type' => 'checkbox',
        'isMulti' => true,
        'dimension' => self::DIMENSION_TYPE_LESSON,
        'key' => 'inclassLpOpenMouthCntFilter',
        'default' => [
            "1次","2次","3次","4次","5次","6次及以上"
        ],
        'items' => [
        ],
    ];

    const EXAM58_PRONUNCIATION_START = [
        'label' => '纠音提交状态',
        'type' => 'checkbox',
        'isMulti' => true,
        'dimension' => self::DIMENSION_TYPE_LESSON,
        'key' => 'exam58PronunciationStar',
        'default' => [],
        'items' => [
        ],
    ];
    const EXAM61_PRONUNCIATION_START = [
        'label' => '配音小达人提交状态',
        'type' => 'checkbox',
        'isMulti' => true,
        'dimension' => self::DIMENSION_TYPE_LESSON,
        'key' => 'exam61PronunciationStar',
        'default' => [],
        'items' => [
        ],
    ];


    //章节筛选
    const LESSON_LIST = [
        'label'       => '章节筛选',   //筛选名称
        'type'        => 'select',     //筛选类型
        'isLesson'    => 1,     //章节选择标志
        'isMulti'     => true,
        'dimension'   => self::DIMENSION_TYPE_LESSON,
        'key'         => Service_Page_Desk_Filter_GetFilterMap::FILTER_LESSON_SELECT_KEY,
        'default'     => [],
        'items'       => [
        ],

    ];

    //错题任务筛选
    const COURSE_WRONG_QUESTION_TASK_LIST = [
        'label'       => '选择错题重做任务',   //筛选名称
        'type'        => 'radio',     //筛选类型
        'isMulti'     => false,
        'dimension'   => self::DIMENSION_TYPE_SCENE,
        'key'         => 'errorBook',
        'default'     => [],
        'items'       => [
        ],

    ];

    const COURSE_WRONG_QUESTION_TASK_STATUS = [
        'label'       => '任务状态',   //筛选名称
        'type'        => 'checkbox',     //筛选类型
        'isMulti'     => true,
        'dimension'   => self::DIMENSION_TYPE_SCENE,
        'key'         => 'courseTaskStatus',
        'default'     => [],
        'items'       => [],
    ];

    const COURSE_WRONG_QUESTION_TASK_PROCESS = [
        'label'       => '任务进度',   //筛选名称
        'type'        => 'checkbox',     //筛选类型
        'isMulti'     => true,
        'dimension'   => self::DIMENSION_TYPE_SCENE,
        'key'         => 'courseTaskProcess',
        'default'     => [],
        'items'       => [
            [
                'text' => '0-25%',
                'value' => 1,
            ],
            [
                'text'  => '25%-50%',
                'value' => 2,
            ],
            [
                'text'  => '50%-85%',
                'value' => 3,
            ],
            [
                'text'  => '85%-100%',
                'value' => 4,
            ]
        ],
    ];


    //巩固练习评级
    const HOMEWORK_LEVEL = [
        'label' => '巩固练习评级',   //筛选名称
        'type'  => 'checkbox',     //筛选类型
        'isMulti' => true,
        'dimension' => self::DIMENSION_TYPE_LESSON,
        'key' => 'homeworkLevel',
        'default' => [],
        'items' => [
        ],

    ];

    //巩固练习评级
    const DEER_HOMEWORK_LEVEL = [
        'label' => '巩固练习评级',   //筛选名称
        'type'  => 'checkbox',     //筛选类型
        'isMulti' => true,
        'dimension' => self::DIMENSION_TYPE_LESSON,
        'key' => 'deerProgrammingHomeworkLevelCommon',
        'default' => [],
        'items' => [
        ],

    ];

    const DEER_PROGRAM_HOMEWORK_STATUS = [
        'label' => '生成报告学生筛选',   //筛选名称
        'type'  => 'checkbox',     //筛选类型
        'isMulti' => true,
        'dimension' => self::DIMENSION_TYPE_LESSON,
        'key' => 'isSubmitBcHomework',
        'default' => [],
        'items' => [
            [
                'text' => '无巩固练习已生成报告',
                'value' => 0
            ],
            [
                'text'  => '有巩固练习已生成报告',
                'value' => 1,
            ]
        ],
    ];

    //相似题评级
    const SIMILAR_HOMEWORK_LEVEL = [
        'label' => '相似题评级',   //筛选名称
        'type'  => 'checkbox',     //筛选类型
        'isMulti' => true,
        'dimension' => self::DIMENSION_TYPE_LESSON,
        'key' => 'similarHomeworkLevel',
        'default' => [],
        'items' => [
        ],
    ];

    //互动题参与率
    const IN_CLASS_INTERACTIVE_QUESTION_PARTICIPATION_RATE = [
        'label' => '互动题参与率',   //筛选名称
        'type'  => 'checkbox',     //筛选类型
        'isMulti' => true,
        'dimension' => self::DIMENSION_TYPE_LESSON,
        'key' => 'inClassInteractiveQuestionParticipationRate',
        'default' => [],
        'items' => [
        ],
    ];

    //互动题正确率
    const IN_CLASS_INTERACTIVE_QUESTION_ACCURACY_RATE = [
        'label' => '互动题正确率',   //筛选名称
        'type'  => 'checkbox',     //筛选类型
        'isMulti' => true,
        'dimension' => self::DIMENSION_TYPE_LESSON,
        'key' => 'inClassInteractiveQuestionAccuracyRate',
        'default' => [],
        'items' => [
        ],
    ];

    const CITY_LEVEL = [
        'label' => '城市线级',
        'type'  => 'checkbox',
        'isMulti' => true,
        'dimension' => self::DIMENSION_TYPE_COURSE,
        'key' => 'systemIntentionCityInfo.city_level',
        'default' => [],
        'items' => [],
    ];

    //fudao、lpc巩固练习评级
    const COMMON_HOMEWORK_LEVEL = [
        'label' => '巩固练习评级',   //筛选名称
        'type'  => 'checkbox',     //筛选类型
        'isMulti' => true,
        'dimension' => self::DIMENSION_TYPE_LESSON,
        'key' => 'commonHomeworkLevel',
        'default' => [],
        'items' => [
        ],
    ];

    //相似题提交次数
    const SIMILAR_HOMEWORK_SUBMIT_NUM = [
        'label' => '相似题提交次数',   //筛选名称
        'type'  => 'checkbox',     //筛选类型
        'isMulti' => true,
        'dimension' => self::DIMENSION_TYPE_LESSON,
        'key' => 'similarHomeworkSubmitNum',
        'default' => [],
        'items' => [
        ],
    ];

    // todo lpc 筛选
    // lpc 请假 复用辅导预到课状态
    const LPC_PRE_ATTEND_STATUS = [
        'label'     => '预到课状态',
        'type'      => 'checkbox',
        'isMulti'   => true,
        'dimension' => self::DIMENSION_TYPE_LESSON,
        'key'       => 'preAttendStatus',
        'default'   => [],
        'items'     => [

        ],
    ];

    const LPC_PRE_ATTEND_STATUS_V2 = [
        'label'     => '预到课状态',
        'type'      => 'checkbox',
        'isMulti'   => true,
        'dimension' => self::DIMENSION_TYPE_LESSON,
        'key'       => 'preAttendStatusV2.preAttendState',
        'default'   => [],
        'items'     => [

        ],
    ];

    //学员意向
    const SYSTEM_INTENTION = [
        'label' => '学员意向',   //筛选名称
        'type'  => 'checkbox',     //筛选类型
        'isMulti' => true,
        'dimension' => self::DIMENSION_TYPE_COURSE,
        'key' => 'systemIntention.systemIntention',
        'default' => [],
        'items' => [
        ],
    ];
    //关注状态
    const INTENTION_STATUS = [
        'label' => '关注状态',   //筛选名称
        'type'  => 'checkbox',     //筛选类型
        'isMulti' => true,
        'dimension' => self::DIMENSION_TYPE_COURSE,
        'key' => 'systemIntention',
        'default' => [],
        'items' => [
        ],

    ];
    //预习状态
    const PREVIEW_STATUS = [
        'label' => '预习状态',   //筛选名称
        'type'  => 'radio',     //筛选类型
        'isMulti' => false,
        'dimension' => self::DIMENSION_TYPE_LESSON,
        'key' => 'lessonPreview.isPreviewed',
        'default' => '',
        'items' => [
        ],

    ];
    //到课状态
    const ATTEND_STATUS = [
        'label' => '到课状态',   //筛选名称
        'type'  => 'radio',     //筛选类型
        'isMulti' => false,
        'dimension' => self::DIMENSION_TYPE_LESSON,
        'key' => 'isAttendWord.isAttendStatus',
        'default' => '',
        'items' => [
        ],

    ];
    //完课状态
    const FINISHED_STATUS = [
        'label' => '完课状态',   //筛选名称
        'type'  => 'radio',     //筛选类型
        'isMulti' => false,
        'dimension' => self::DIMENSION_TYPE_LESSON,
        'key' => 'isFinishWord.isFinishWordStatus',
        'default' => '',
        'items' => [
        ],

    ];
    //转化状态

    const TRANSFER_STATUS = [
        'label' => '转化状态',   //筛选名称
        'type'  => 'radio',     //筛选类型
        'isMulti' => false,
        'dimension' => self::DIMENSION_TYPE_COURSE,
        'key' => 'lpcPhone.isTrans',
        'default' => '',
        'items' => [
        ],

    ];

    const BOTTOM_TEST_TYPE = [
        'label' => '摸底测类型',
        'type'  => 'checkbox',
        'isMulti' => true,
        'dimension' => self::DIMENSION_TYPE_COURSE,
        'key' => 'preCompliTestData.preCompliTestData_*',
        'default' => [],
        'items' => [

        ],

    ];
    const BOTTOM_TEST_SCORE = [
        'label' => '摸底测分数',
        'type'  => 'checkbox',
        'isMulti' => true,
        'dimension' => self::DIMENSION_TYPE_COURSE,
        'key' => 'preCompliTestData.preCompliTestScore_*',
        'default' => [],
        'items' => [

        ],
    ];
    const BOTTOM_TEST_FUDAO_TYPE = [
        'label' => '完成摸底测',
        'type'  => 'select',
        'isMulti' => false,
        'dimension' => self::DIMENSION_TYPE_COURSE,
        'key' => 'placementTestFinishStatusFudaoStatus',
        'default' => [],
        'items' => [],
    ];
    //触达次数
    const WXCARD_SEND_TIMES = [
        'label'     => '按累积触达次数选择学员',
        'type'      => 'checkbox',
        'isMulti'   => true,
        'dimension' => self::DIMENSION_TYPE_SCENE,
        'key'       => 'wxcardsendtimes',
        'default' => [0,1,2,3,-1],
        'items' => [
            [
                'text'  => '未触达',
                'value' => 0,
            ],
            [
                'text' => '1次',
                'value' => 1,
            ],
            [
                'text'  => '2次',
                'value' => 2,
            ],
            [
                'text'  => '3次',
                'value' => 3,
            ],
            [
                'text'  => '超过3次',
                'value' => -1,
            ],
        ],
    ];
    /**
     * @param $scene int
     * @return bool
     */
    public function __construct()
    {
    }

    private static function _getCourseTaskList($courseId): array
    {
        $result = [];
        $taskList = Api_ExerciseNoteTask::getCourseTaskList($courseId);
        $default = AssistantDesk_ExerciseNoteTask::getDefaultTask($taskList);
        if(empty($taskList)){
            return $result;
        }
        foreach ($taskList as $taskInfo){
            $taskName = $taskInfo['taskName'];
            if($taskInfo['status'] == 1){
                $taskName .= "(未解锁)";
            }
            $result[] = [
                'text' => $taskName,
                'value' => $taskInfo['taskId'],
            ];
        }
        return [$result,$default];
    }

    /**
     * 获取课程下的自定义标签列表
     * @param int $courseId 课程ID
     * @param int $assistantUid 助教ID
     * @param int $tagType 标签类型
     * @return array 返回标签列表，每个标签包含text和value
     */
    private static function getCustomTagList($courseId, $assistantUid, $tagType) {
        if (!$courseId) {
            return [];
        }

        $result = [];

        try {
            // 获取课程下的所有自定义标签
            $customTagService = new Service_Data_Desk_CustomTagCourseStudent();

            // 获取课程下的学生列表
            $studentList = Api_Allocate::getNormalLeadsByCourseAssistant($courseId, $assistantUid);

            if (!empty($studentList)) {
                $studentUids = array_column($studentList, 'studentUid');
                if (!empty($studentUids)) {
                    $tags = $customTagService->getTagsByCUT(
                        $courseId,
                        $studentUids,
                        $tagType
                    );

                    // 格式化标签数据
                    if (!empty($tags)) {
                        foreach ($tags as $tag) {
                            if (isset($tag['tag'])) {
                                $result[] = [
                                    'text' => $tag['tag'],
                                    'value' => $tag['tag']
                                ];
                            }
                        }
                    }

                    // 去重
                    $result = array_values(array_unique($result, SORT_REGULAR));
                }
            }

        } catch (Exception $e) {
            Bd_Log::warning("获取自定义标签列表失败: " . $e->getMessage());
        }

        return $result;
    }

    /**
     * 格式化统一筛选所能识别的过滤条件，将场景化群发的过滤条件，push到统一筛选里面
     * @param $params
     * @param array $filterConf
     */
    private function formatFilter(&$params, $filterConf = []) {
        $params['filter'] = [];
        //场景化群发默认筛选
        if (!empty($filterConf)) {
            $needKeyMap  =[];
            foreach ($filterConf as $needkey => $v) {
                if (!is_null($v)) {
                    $needKeyMap[$needkey] = $v;
                }
            }
            if ($needKeyMap) {
                $params['filter'] = (new Service_Page_Desk_Filter_GetFilterMap())->getFilterByNeedKeyMapFilterValue($params['courseId'], $needKeyMap);
                Bd_Log::notice("场景化群发获取 filter courseId ".$params['courseId']." filterConf ".json_encode($filterConf). " ret ".json_encode($params['filter']));
            }
        }

        //todo sceneFilter
        if (!empty($params['sceneFilter'])) {
            Bd_Log::notice("场景化群发获取 filter  ".json_encode($params['filter']));
            $params['filter'] = self::mergeFilters($params['filter'], json_decode($params['sceneFilter'], true));
            Bd_Log::notice("场景化群发获取 sceneFilterParam ".json_encode($params['sceneFilter']). " ret ".json_encode($params['filter']));

        }
    }

    private static function mergeFilters($defaultFilter, $selectFilter): array {
        if (!$selectFilter) {
            $selectFilter = [];
        }
        $returnMerge = $selectFilter;
        foreach($defaultFilter as $type => $typeSelectMap) {
            foreach($typeSelectMap as $selectField => $selectValue) {
                if (!isset($returnMerge[$type][$selectField])) {
                    $returnMerge[$type][$selectField] = $selectValue;
                }
            }
        }
        return $returnMerge;
    }

    /**
     * 获取统一筛选的学员列表
     * @param $arrInput
     * @return array | bool
     * @throws Common_Exception
     * @throws ReflectionException
     */
    public function getCommonStudentList($arrInput) {
        if (!$arrInput['sendType']) {
            Bd_Log::warning("场景化群发获取统一筛选列表 获取场景失败 ".json_encode($arrInput));
            return [];
        }

        $filterConf = self::SCENE_NEED_KEY[$arrInput['sendType']] ?? [];
        $this->formatFilter($arrInput, $filterConf);

        $needKeys = $this->getNeedKeys($filterConf);
        Bd_Log::notice("场景化群发获取统一筛选列表 参数：".json_encode($arrInput) ." needKeys ".json_encode($needKeys));

        $stuObj = (new Service_Page_Desk_Filter_StudentList())->studentListByNeedKeys($arrInput, $needKeys);
        Bd_Log::notice("场景化群发获取统一筛选列表 返回：".json_encode($stuObj));
        if (!$stuObj['returnStudentList']) {
            return [];
            // 没有学生，不给报错
            // throw new Common_Exception(Common_ExceptionCodes::DESK_OTHER_ERROR, '场景化群发获取统一筛选学生数据错误');
        }

        $studentList = array_column($stuObj['returnStudentList'] ?? [], null,'studentUid');
        Bd_Log::notice("场景化群发获取统一筛选列表 格式化后结果：".json_encode($studentList));

        return $studentList;
    }


    public function getCommonSmsStudentList($arrInput)  {
        $isLpc = AssistantDesk_Data_Course::isLpcByCourse($arrInput['courseId']);
        if ($isLpc) {
            $filterConf = self::
                SCENE_NEED_KEY_SMS_LPC[$arrInput['sendType']] ?? [];
        } else {
            $filterConf = self::SCENE_NEED_KEY_SMS[$arrInput['sendType']] ?? [];
        }
        $this->formatFilter($arrInput, $filterConf);

        $needKeys = $this->getNeedKeys($filterConf);
        Bd_Log::notice("场景化群发短信获取统一筛选列表 参数：".json_encode($arrInput) ." needKeys ".json_encode($needKeys));

        $stuObj = (new Service_Page_Desk_Filter_StudentList())->studentListByNeedKeys($arrInput, $needKeys);
        Bd_Log::notice("场景化群发短信获取统一筛选列表 返回：".json_encode($stuObj));
        if (!$stuObj['returnStudentList']) {
            return [];
            // throw new Common_Exception(Common_ExceptionCodes::DESK_OTHER_ERROR, '场景化群发短信获取统一筛选学生数据错误');
        }

        $studentList = array_column($stuObj['returnStudentList'] ?? [], null,'studentUid');
        Bd_Log::notice("场景化群发短信获取统一筛选列表 格式化后结果：".json_encode($studentList));

        return $studentList;
    }
    // todo
    private function getNeedKeys($filterConf) {
        $ret = [];
        if(!empty($filterConf)) {
            foreach ($filterConf as $needKeys => $val) {
                if(false !== strpos($needKeys, '*')) {
                    continue;
                }
                $ret[] = $needKeys;
            }
        }
        return $ret;
    }

    private static function getCorrectSelectList($courseId, $lessonId, $assistantUid) {
        $studentCorrectTimes = Api_PcAssistant::getCorrectTimes($courseId, $lessonId, $assistantUid);
        if ($studentCorrectTimes === false || !is_array($studentCorrectTimes)) {
            throw new Common_Exception(Common_ExceptionCodes::DESK_API_ERROR, '获取学生巩固练习批改次数失败');
        }
        $optionList = [];
        $correctTimesMap = [];
        $hasDefault = false;
        foreach ($studentCorrectTimes as $studentUid => $correctTimes) {
            if (!$correctTimes) {
                continue;
            }
            if (isset($correctTimesMap[$correctTimes])) {
                continue;
            }
            $correctTimesMap[$correctTimes] = $correctTimes;
            $optionList[] = [
                'text'  => "第{$correctTimes}次批改",
                'value' => $correctTimes,
            ];
        }
        return $optionList;
    }

    private static function getCorrectSelect($courseId, $lessonId, $assistantUid) {
        $correctTimes = self::CORRECT_TIMES;
        $items = Service_Page_Desk_Filter_GetFilterMap::getCorrectSelectList($courseId, $lessonId, $assistantUid);
        if (!$items) {
            throw new Common_Exception(Common_ExceptionCodes::DESK_TIPS, '无学生巩固练习批改次数');
        }
        $correctTimes['default'] = $items[0]['value'];
        return $correctTimes;
    }


    /**
     * @param $courseId
     * @param $assistantUid
     * @param $sendType
     * @return array
     * @throws Common_Exception
     */
    private static function getCuoTiBenDefaultLessonIds($courseId, $assistantUid, $sendType): array {
        $arrConds   = [
            'courseId'      => $courseId,
            'assistant_uid' => $assistantUid,
            'type'          => $sendType == Assistant_Ds_WxMessageSendRecord::SEND_TYPE_CUOTIBEN_JIEXI ? Service_Data_StudentMistake::TYPE_JIEXI : Service_Data_StudentMistake::TYPE_ZUODA,
            'deleted'       => Service_Data_StudentMistake::DELETED_NO,
        ];
        $arrConds[] = 'status in (' . implode(',', [Service_Data_StudentMistake::STATUS_2, Service_Data_StudentMistake::STATUS_1]) . ')';

        $objStudentMistake = new Service_Data_StudentMistake();

        $mixedRet = $objStudentMistake->getListByConds($arrConds);
        if (false === $mixedRet) {
            throw new Common_Exception(Common_ExceptionCodes::DESK_DB_SELECT_FAILED, '查询学生的错题本附件失败');
        }


        //返回错题本pdfUrl的lessonId
        $url = $mixedRet[0]['url'];
        parse_str(substr($url, 1+strpos($url, '?')) ?: '' , $arr);
        $filterDefaultValue = !empty($arr['lessonIds']) ? explode(',', $arr['lessonIds']) ? array_map('intval', explode(',', $arr['lessonIds'])): [] : [];
        return $filterDefaultValue;
    }


    private static function getMonthlyExamTimesSelectList($courseId): array {
        $mixedRet = Api_Hx::getMonthlyExamConfig($courseId);
        if (false === $mixedRet) {
            throw new Common_Exception(Common_ExceptionCodes::DESK_NETWORK_ERROR, '获取月考配置失败');
        }
        $optionList = [];
        $arrMonthlyExamConf = $mixedRet['monthlytest'][$courseId];
        $arrIndex = [];
        foreach ($arrMonthlyExamConf as $k => $row) {
            $arrIndex[] = ($k + 1);
        }


        foreach ($arrIndex as $idx) {
            $optionList[] = [
                'text' => sprintf('第%s次月考', $idx),
                'value' => $idx,
            ];
        }
        return $optionList;
    }



    public function getFilterItem($arrInput){
        if (!$arrInput['sendType']) {
            Bd_Log::warning("参数错误 获取场景化群发筛选配置 sendType ".$arrInput['sendType']);
            return false;
        }

        $courseId     = $arrInput['courseId'];
        $assistantUid = $arrInput['assistantUid'];
        $personUid    = $arrInput['personUid'];
        $lessonId     = $arrInput['lessonId'];
        $taskId       = $arrInput['taskId'] ?? [];
        $sendType     = $arrInput['sendType'];
        $courseInfo   = AssistantDesk_Course::getCourseInfo($courseId);
        $lessonInfo = Api_Dal::getLessonBaseByLessonIds($lessonId, ['courseId', 'lessonId', 'stopTime', 'playType']);
        $conf = self::SCENE_CONF[$arrInput['sendType']] ?? [];
        $conf['grade']  = $courseInfo['mainGradeId'];

        $conf['sendAbility'] = AssistantDesk_KpMessageService_MessageSendPloy::sendTypeToAbility($assistantUid, $sendType);
        $conf['sendGray']    = AssistantDesk_KpMessageService_MessageSendPloy::sendTypeInGrayForWXRule($sendType);

        switch ($arrInput['sendType']) {
            // 需要特殊处理的场景
            case Assistant_Ds_WxMessageSendRecord::SEND_TYPE_ATTEND_LPC:
                $grayRes = $this->checkSendCard($courseInfo,$lessonInfo);
                $conf['hasSendCard']['isShow'] = $grayRes;

                // 命中灰度，则替换 filter
                $key = 'ai_pre_attend_gray';
                if (Api_Assistantdeskgo_Api::grayHit($personUid, $key)) {
                    $conf['sceneFilter'] = [
                        self::LPC_PRE_ATTEND_STATUS_V2,
                    ];
                }
                break;
            case Assistant_Ds_WxMessageSendRecord::SEND_TYPE_ATTEND:
            case Assistant_Ds_WxMessageSendRecord::SEND_TYPE_GUANJIA_ATTEND:
                $grayRes = $this->checkSendCard($courseInfo,$lessonInfo);
                $conf['hasSendCard']['isShow'] = $grayRes;
                break;
//            case Assistant_Ds_WxMessageSendRecord::SEND_TYPE_SUBMIT_MONTHLY_EXAM:
//                $examTImes = self::MONTHLY_EXAM_UN_FINISH;
//                $examTImes['items'] = self::getMonthlyExamTimesSelectList($courseId);
//                $conf['sceneFilter'] = [$examTImes];
//                break;
            case Assistant_Ds_WxMessageSendRecord::SEND_TYPE_SATISFACTION_QUE:
                // 处理图片
                $queCond                = [
                    'year'         => $courseInfo['year'],
                    'season'       => $courseInfo['season'],
                    'type'         => Assistant_Ds_FirstLineSurveyQuestionConfig::TYPE_1,
                    'department'   => Zb_Const_GradeSubject::$GRADEMAPXB[$courseInfo['mainGradeId']],
                    'subject'      => $courseInfo['mainSubjectId'],
                    'grade'        => $courseInfo['mainGradeId'],
                    'assistantUid' => $assistantUid,
                    'courseId'     => $courseId,
                ];
                $queId = Assistant_Ds_FirstLine_Survey::getBindSurveyQueId($queCond);
                $queUrl = Assistant_Ds_FirstLine_Survey::getSurveyUrl($assistantUid, $courseId, $queId);
                $queUrl = $queUrl ? Api_Su::getShortUrl($queUrl . "&refer=messagesend") : "";
                $queryParams['grade'] = Zb_Const_GradeSubject::$GRADE[$courseInfo['mainGradeId']] ?? '';
                $queryParams['subject'] = AssistantDesk_Common_Keyconst::getSubject()[$courseInfo['mainSubjectId']] ?? '';
                $assistant = Api_UserProfile::getUserSsites($assistantUid);
                $queryParams['uname'] = $assistant['record']['shortUserName'];//简称
                $queryParams['url'] = $queUrl;
                $middlePageUrl = 'https://www.zybang.com/assistantweb/wxview/open-app?'.http_build_query($queryParams);
                $conf['imgSrc'] = $middlePageUrl;
                break;
            case Assistant_Ds_WxMessageSendRecord::SEND_TYPE_16 :
                $key = $courseInfo['year'].'_'.$courseInfo['season'];
                Bd_Log::notice("场景化群发获取课程数据 {$arrInput['courseId']} key {$key}  ret ：". self::INTERVIEW_URL_MAP[$key]['url']);
                $conf['imgSrc'] = self::INTERVIEW_URL_MAP[$key]['url'] ?? '';
                break;
            case Assistant_Ds_WxMessageSendRecord::SEND_TYPE_CORRECT_FEEDBACK:
                $correctTimes        = self::getCorrectSelect($courseId, $lessonId, $assistantUid);
                $conf['sceneFilter'] = [self::IS_SEND_LESSON_PG, $correctTimes, self::HOMEWORK_LEVEL];
                break;
            case Assistant_Ds_WxMessageSendRecord::SEND_TYPE_CORRECT_FEEDBACK_DEER:
                $conf['sceneFilter'] = [self::IS_SEND_LESSON_PG, self::DEER_HOMEWORK_LEVEL];
                break;
            case Assistant_Ds_WxMessageSendRecord::SEND_TYPE_COMPOSITION_REPORT:
                //处理图片
                $conf['imgSrc']      = 'https://img.zuoyebang.cc/zyb_1b866e1d9400e6f4536979a5d0b1d09e.png';

                // 如果是初中英语，就换下图
                if ((Zb_Const_GradeSubject::GRADE_STAGE_JUNIOR === Zb_Const_GradeSubject::$GRADEMAPXB[$courseInfo['mainGradeId']])
                    && (Zb_Const_GradeSubject::ENGLISH === $courseInfo['mainSubjectId'])
                ) {
                    $conf['imgSrc'] = 'https://img.zuoyebang.cc/zyb_a9497f3bd095efb2124e68227607c09f.png';
                }

                $correctTimes        = self::getCorrectSelect($courseId, $lessonId, $assistantUid);
                $conf['sceneFilter'] = [self::IS_SEND_LESSON, $correctTimes, self::HOMEWORK_LEVEL];
                break;
            case Assistant_Ds_WxMessageSendRecord::SEND_TYPE_COMPOSITION_REPORT_COMMON:
                //处理图片
                $conf['imgSrc']      = 'https://img.zuoyebang.cc/zyb_1b866e1d9400e6f4536979a5d0b1d09e.png';

                // 如果是初中英语，就换下图
                if ((Zb_Const_GradeSubject::GRADE_STAGE_JUNIOR === Zb_Const_GradeSubject::$GRADEMAPXB[$courseInfo['mainGradeId']])
                    && (Zb_Const_GradeSubject::ENGLISH === $courseInfo['mainSubjectId'])
                ) {
                    $conf['imgSrc'] = 'https://img.zuoyebang.cc/zyb_a9497f3bd095efb2124e68227607c09f.png';
                }

                $correctTimes        = self::getCorrectSelect($courseId, $lessonId, $assistantUid);
                $conf['sceneFilter'] = [self::IS_SEND_LESSON, $correctTimes, self::COMMON_HOMEWORK_LEVEL];
                break;
            case Assistant_Ds_WxMessageSendRecord::SEND_TYPE_CUOTIBEN_JIEXI:
                $lessonList            = self::LESSON_LIST;
                $lessonList['default'] = self::getCuoTiBenDefaultLessonIds($courseId, $assistantUid, $arrInput['sendType']);
                $conf['sceneFilter']   = [$lessonList, self::IS_L2R_STATUS];
                break;
            case Assistant_Ds_WxMessageSendRecord::SEND_TYPE_CUOTIBEN_ZUODA:
                $lessonList            = self::LESSON_LIST;
                $lessonList['default'] = self::getCuoTiBenDefaultLessonIds($courseId, $assistantUid, $arrInput['sendType']);
                $conf['sceneFilter']   = [$lessonList];
                break;
            case 67:
                $switchInfo = (new Service_Data_AssistantAutoWxCardSwitch())->getRecord(['assistantUid' => $assistantUid, 'courseId' => $courseId]);
                $conf['switchStatus'] = empty($switchInfo) ? Service_Data_AssistantAutoWxCardSwitch::STATUS_CLOSE : $switchInfo['status'];
                $wxCardTaskUsed = Api_Muse::getCardTaskUsed($assistantUid);
                $conf['remainCount'] = $wxCardTaskUsed['data']['stuToteac']['lastNum'] ?? 0;
                $conf['totalCount']  = $wxCardTaskUsed['data']['stuToteac']['totalNum'] ?? 0;
                break;
            case Assistant_Ds_WxMessageSendRecord::SEND_TYPE_LEARN_REPORT_FD:
            case Assistant_Ds_WxMessageSendRecord::SEND_TYPE_LEARN_REPORT_CAMBRIDGE_ENGLISH:
                //
                if (Zb_Const_GradeSubject::GRADE_STAGE_PRIMARY === Zb_Const_GradeSubject::$GRADEMAPXB[$courseInfo['mainGradeId']]) {
                    //小学
                    $conf['sceneFilter'][]   = self::WATCH_ONCOURSE_STATUS;
                    $conf['sceneFilter'][]   = self::ATTEND_FINISH_STATUS;
                }else if (Zb_Const_GradeSubject::GRADE_STAGE_JUNIOR === Zb_Const_GradeSubject::$GRADEMAPXB[$courseInfo['mainGradeId']]) {
                    //初中
                    $conf['sceneFilter'][] = self::WATCH_ONCOURSE_ATTEND_STATUS;
                    $conf['sceneFilter'][] = self::ATTEND_FINISH_STATUS_JUNIOR;
                }else if(Zb_Const_GradeSubject::GRADE_STAGE_SENIOR === Zb_Const_GradeSubject::$GRADEMAPXB[$courseInfo['mainGradeId']]){
                    //高中
                    $conf['sceneFilter'][] = self::WATCH_ONCOURSE_ATTEND_STATUS;
                    $conf['sceneFilter'][] = self::ATTEND_FINISH_STATUS_SENIOR;
                }
                $conf['sceneFilter'][] = self::USER_TYPE;
                $conf['sceneFilter'][] = self::INTERACTION_SUBMIT_STATUS;
                $conf['sceneFilter'][] = self::INTERACTION_RIGHT_STATUS;
                $conf['sceneFilter'][] = self::INTERACTION_SUBMIT_RATE;
                $conf['sceneFilter'][] = self::INTERACTION_RIGHT_RATE;
                if ($arrInput['sendType'] == Assistant_Ds_WxMessageSendRecord::SEND_TYPE_LEARN_REPORT_CAMBRIDGE_ENGLISH) {
                    //剑桥英语课堂报告要加一个筛选
                    $conf['sceneFilter'][] = self::ATTEND_OPEN_MOUTH_CNT;
                }

                break;
            case Assistant_Ds_WxMessageSendRecord::SEND_TYPE_EVALUATE:
                $deviceInfo = (new Service_Data_Sip_DeviceInfo())->getDeviceInfo($assistantUid);
                $conf["teacherName"] = $deviceInfo['nickname'] ?? "";

                $wxUserInfo = Api_KunpengEnterprise::getWxUserInfo($assistantUid);
                $conf["teacherAvatarUrl"] = $wxUserInfo['avatar'] ?? "";
                $url = $this->getEvaluateUrl($taskId, $courseId, $assistantUid);
                $conf["reportUrl"] = $url ? Common_Singleton::getInstanceData(Api_Su::class, 'getShortUrl', [$url]) : $url;
                break;
            case Assistant_Ds_WxMessageSendRecord::SEND_TYPE_COURSE_TIME_TABLE:
                $reportUrl = $this->getCourseTimeUrl();
                if (empty($reportUrl)){
                    $reportUrl = "https://support.zuoyebang.com/static/survey-questionnaire/course-schedule/#/courseschedule/list";
                }
                $conf["reportUrl"] = $reportUrl;
                break;
            case Assistant_Ds_WxMessageSendRecord::SEND_TYPE_LEARNING_REPORT : // 学情测评
                switch (Zb_Const_GradeSubject::$GRADEMAPXB[$courseInfo['mainGradeId']]) {
                    case Zb_Const_GradeSubject::GRADE_STAGE_PRIMARY:
                        break;
                    case Zb_Const_GradeSubject::GRADE_STAGE_JUNIOR:
                        $filter = self::FILTER_PROBE_PDF_DONE_JUNIOR;
                        $filter['default'] = [0];
                        $conf['sceneFilter'][] = $filter;
                        $conf['imgSrc'] = 'https://img.zuoyebang.cc/zyb_cc63db0400dd9abc883ef28c31cc8784.png';
                        $reportUrl = Api_Evaluate_LearningReport::getLearningReportLink($courseInfo['mainGradeId'], Api_Evaluate_LearningReport::LEARNING_REPORT_TEST);
                        if (!empty($reportUrl)) {
                            $reportUrl = sprintf($reportUrl, Hk_Util_IdCrypt::encodeAQid($courseId), Hk_Util_IdCrypt::encodeAQid($assistantUid));
                            $conf['reportUrl'] = Api_Su::getShortUrl($reportUrl);
                        }
                        break;
                    case Zb_Const_GradeSubject::GRADE_STAGE_SENIOR:
                        $filter = self::FILTER_PROBE_PDF_DONE_SENIOR;
                        $filter['default'] = [0];
                        $conf['sceneFilter'][] = $filter;
                        $conf['sceneFilter'][] = self::USER_TYPE;
                        $conf['imgSrc'] = 'https://img.zuoyebang.cc/zyb_5e584e0b3cd93e43df4b51b70792af70.png';
                        $reportUrl = Api_EduProbe::getProbeUrl($courseId);
                        if (!empty($reportUrl)) {
                            $conf['reportUrl'] = Api_Su::getShortUrl($reportUrl);
                        }
                        break;
                }

                $key = $courseInfo['year'].'_'.$courseInfo['season'];
                Bd_Log::notice("场景化群发获取课程数据 {$arrInput['courseId']} key {$key}  ret ：". self::INTERVIEW_URL_MAP[$key]['url']);
                break;
            case Assistant_Ds_WxMessageSendRecord::SEND_TYPE_SUBJECT_DIAG : // 群发学科测评
                switch (Zb_Const_GradeSubject::$GRADEMAPXB[$courseInfo['mainGradeId']]) {
                    case Zb_Const_GradeSubject::GRADE_STAGE_PRIMARY:
                        break;
                    case Zb_Const_GradeSubject::GRADE_STAGE_JUNIOR:  // 初中
                        $filter = self::FILTER_PROBE_PDF_DONE_JUNIOR;  // 报告是否生成
                        $filter['default'] = [0];  // 群发测评时，默认向没有报告的学员发；群发报告时，默认向有报告的学员发
                        $conf['sceneFilter'][] = $filter;
                        $conf['imgSrc'] = 'https://img.zuoyebang.cc/zyb_cc63db0400dd9abc883ef28c31cc8784.png';
                        $evaluateInfo = Api_Evaluate_EvaluateProbe::getProbeEvaluate(AssistantDesk_Data_CommonParams::$courseId);
                        $evaluateId = $evaluateInfo['evaluateId'];

                        if (!empty($evaluateInfo['evaluateUrlCpPrefix']) && !empty($evaluateInfo['cpQueryFormat'])) {
                            $testUrl = $evaluateInfo['evaluateUrlCpPrefix'] . sprintf($evaluateInfo['cpQueryFormat'],
                                    Hk_Util_IdCrypt::encodeAQid($evaluateId), Hk_Util_IdCrypt::encodeAQid(AssistantDesk_Data_CommonParams::$courseId), Hk_Util_IdCrypt::encodeAQid(AssistantDesk_Data_CommonParams::$assistantUid));
                            $conf['reportUrl'] = Api_Su::getShortUrl($testUrl);
                        }
                        break;
                    case Zb_Const_GradeSubject::GRADE_STAGE_SENIOR:
                        $filter = self::FILTER_PROBE_PDF_DONE_SENIOR;
                        $filter['default'] = [0];
                        $conf['sceneFilter'][] = $filter;
                        $conf['sceneFilter'][] = self::USER_TYPE;
                        $conf['imgSrc'] = 'https://img.zuoyebang.cc/zyb_5e584e0b3cd93e43df4b51b70792af70.png';

                        $reportUrl = Api_EduProbe::getProbeUrl($courseId);
                        if (!empty($reportUrl)) {
                            $conf['reportUrl'] = Api_Su::getShortUrl($reportUrl);
                        }
                        break;
                }
                break;
            case Assistant_Ds_WxMessageSendRecord::SEND_TYPE_PERSONAL_LEARN_FEEDBACK:
                $exam9 = \Api_Assistantdeskgo_Api::getSceneContextOptions($assistantUid, $courseId, 0, $sendType, 'exam9');
                if ($exam9 && $exam9['list']) {
                    $conf['sceneContext'][0]['items'] = $exam9['list'];
                }
                $lesson = \Api_Assistantdeskgo_Api::getSceneContextOptions($assistantUid, $courseId, 0, $sendType, 'lesson');
                if ($lesson && $lesson['list']) {
                    $conf['sceneContext'][1]['items'] = $lesson['list'];
                }
                break;
            case Assistant_Ds_WxMessageSendRecord::SEND_TYPE_DEER_PROGRAM_REPORT:
            case Assistant_Ds_WxMessageSendRecord::SEND_TYPE_RJYL_REPORT:
            case Assistant_Ds_WxMessageSendRecord::SEND_TYPE_DEER_PROGRAM_REPORT_YOUKETANG:
                $iconConfig = Api_Mercury::GetConfigForJson(self::CONFIG_KEY_SENDCARTD_ICON);
                $conf['hasSendCard']['defaultPic'] = $iconConfig ?? [];
                break;
            case Assistant_Ds_WxMessageSendRecord::SEND_TYPE_STAGE_RESULT_FEEDBACK_CARD:
                $iconConfig = Api_Mercury::GetConfigForJson(self::CONFIG_KEY_SENDCARTD_ICON_Stage_Result_FeedBack);
                $conf['hasSendCard']['defaultPic'] = $iconConfig ?? [];
                break;
            case Assistant_Ds_WxMessageSendRecord::SEND_TYPE_SUBJECT_DIAG_REPORT:
            case Assistant_Ds_WxMessageSendRecord::SEND_TYPE_EDU_PROBE_PDF: // 学情报告
                switch (Zb_Const_GradeSubject::$GRADEMAPXB[$courseInfo['mainGradeId']]) {
                    case Zb_Const_GradeSubject::GRADE_STAGE_PRIMARY:
                        break;
                    case Zb_Const_GradeSubject::GRADE_STAGE_JUNIOR:
                        $filter = self::FILTER_PROBE_PDF_DONE_JUNIOR;  // 报告是否生成
                        $filter['default'] = [1];  // 群发测评时，默认向没有报告的学员发；群发报告时，默认向有报告的学员发
                        $conf['sceneFilter'][] = $filter;
                        break;
                    case Zb_Const_GradeSubject::GRADE_STAGE_SENIOR:
                        $filter = self::FILTER_PROBE_PDF_DONE_SENIOR;
                        $filter['default'] = [1];
                        $conf['sceneFilter'][] = $filter; // 是否生成学情报告
                        break;
                }
                break;
            case Assistant_Ds_WxMessageSendRecord::SEND_TYPE_PLAYBACK_LPC:
            case Assistant_Ds_WxMessageSendRecord::SEND_TYPE_SUBMIT_HOMEWORK_LPC:
            case Assistant_Ds_WxMessageSendRecord::SEND_TYPE_LPC_SMS_ATTEND:
                // 命中灰度，则替换 filter
                $key = 'ai_pre_attend_gray';
                if (Api_Assistantdeskgo_Api::grayHit($personUid, $key)) {
                    $conf['sceneFilter'] = [
                        self::LPC_PRE_ATTEND_STATUS_V2,
                    ];
                }
                break;
            case Assistant_Ds_WxMessageSendRecord::SEND_TYPE_EXERCISE_NOTE_TASK:
            case Assistant_Ds_WxMessageSendRecord::SEND_TYPE_EXERCISE_NOTE_CLEAN_REPORT:
                $courseTaskList = self::COURSE_WRONG_QUESTION_TASK_LIST;
                $courseTaskList['isMulti'] = false;
                $courseTaskList['type'] = 'radio';
                list($courseTaskList['items'], $default) = self::_getCourseTaskList($courseId);
                $courseTaskList['default'] = $default[0] ?? 0;
                $conf['sceneFilter'] = [self::IS_SEND,$courseTaskList,self::COURSE_WRONG_QUESTION_TASK_STATUS,self::COURSE_WRONG_QUESTION_TASK_PROCESS];
                break;
            case Assistant_Ds_WxMessageSendRecord::SEND_TYPE_EXERCISE_NOTE_TASK_ZUODA:
            case Assistant_Ds_WxMessageSendRecord::SEND_TYPE_EXERCISE_NOTE_TASK_JIEXI:
                $courseTaskList = self::COURSE_WRONG_QUESTION_TASK_LIST;
                list($courseTaskList['items'], $default) = self::_getCourseTaskList($courseId);
                $courseTaskList['default'] = $default[0] ?? 0;
                $conf['sceneFilter'] = [$courseTaskList,self::COURSE_WRONG_QUESTION_TASK_STATUS];
                break;
            case Assistant_Ds_WxMessageSendRecord::SEND_TYPE_BOTTOM_TEST_REPORT:
            case Assistant_Ds_WxMessageSendRecord::SEND_TYPE_BOTTOM_TEST_REPORT_FD:
                $subjectList = \AssistantDesk_Data_Duxuesc_DataCenter::getBottomTestSubjectList();
                if ($subjectList) {
                    $subjectIds = array_unique(array_values($subjectList));
                    $subjectMap = AssistantDesk_Common_Keyconst::getSubject();
                    foreach ($subjectIds as $subjectId) {
                        $subjectName = $subjectMap[$subjectId];
                        $conf['urlCaptureSrcMap'][$subjectName . "摸底测图片"] = ["https://img.zuoyebang.cc/zyb_b447d7c3b447318a4cae0dda9aa38960.png"];
                        $conf['hasSendCardMap'][$subjectName . "摸底测卡片"] = [
                            'isShow' => true,
                            'sendCardField' => '#' . $subjectName . '摸底测报告链接#',
                            'defaultPic' => [
                                ["url"=>"https://img.zuoyebang.cc/zyb_d050f4cda11335e5e4b97b1dc03e6709.png","iconType"=>4],
                                ["url"=>"https://img.zuoyebang.cc/zyb_7c1382c118a82d8e5ba6cc9918f091ab.png","iconType"=>3],
//                                ["url"=>"https://img.zuoyebang.cc/zyb_08e443d876b668c16b006028848e176c.png","iconType"=>2],
                                ["url"=>"https://img.zuoyebang.cc/zyb_e8e9fd911f851fd27943e900e9e2a951.png","iconType"=>8],
                                ["url"=>"https://img.zuoyebang.cc/zyb_a7a61fa61cb81a8c32afb1fb3e42b245.png","iconType"=>11],
                            ],
                            'maxPicCnt' => 5,
                            'defaultTitle' => ['#学生名#', '请查收你的' . $subjectName . '摸底测报告'],
                            'cardVariable' => ["学生名", "课程名称", "学科"],
                            'introductionVariable' => ["学生名", "课程名称", "学科"],
                            'defaultIntroduction' => ["点击这里，快速查看"]
                        ];
                    }
                }
                break;
            case Assistant_Ds_WxMessageSendRecord::SEND_TYPE_PRONUNCIATION_REPORT_GROUP:
            case Assistant_Ds_WxMessageSendRecord::SEND_TYPE_VOICE_REPORT_GROUP:
                if (Zb_Const_GradeSubject::GRADE_STAGE_PRIMARY === Zb_Const_GradeSubject::$GRADEMAPXB[$courseInfo['mainGradeId']]) {
                    //小学
                    $conf['sceneFilter'][] = self::WATCH_ONCOURSE_STATUS;
                    $conf['sceneFilter'][] = self::ATTEND_FINISH_STATUS;
                } else if (Zb_Const_GradeSubject::GRADE_STAGE_JUNIOR === Zb_Const_GradeSubject::$GRADEMAPXB[$courseInfo['mainGradeId']]) {
                    //初中
                    $conf['sceneFilter'][] = self::WATCH_ONCOURSE_ATTEND_STATUS;
                    $conf['sceneFilter'][] = self::ATTEND_FINISH_STATUS_JUNIOR;
                } else if (Zb_Const_GradeSubject::GRADE_STAGE_SENIOR === Zb_Const_GradeSubject::$GRADEMAPXB[$courseInfo['mainGradeId']]) {
                    //高中
                    $conf['sceneFilter'][] = self::WATCH_ONCOURSE_ATTEND_STATUS;
                    $conf['sceneFilter'][] = self::ATTEND_FINISH_STATUS_SENIOR;
                }
                $conf['sceneFilter'][] = self::USER_TYPE;
                if ($arrInput['sendType'] == Assistant_Ds_WxMessageSendRecord::SEND_TYPE_PRONUNCIATION_REPORT_GROUP) {
                    //剑桥英语课堂报告要加一个筛选
                    $conf['sceneFilter'][] = self::EXAM58_PRONUNCIATION_START;

                }
                if ($arrInput['sendType'] == Assistant_Ds_WxMessageSendRecord::SEND_TYPE_VOICE_REPORT_GROUP) {
                    //剑桥英语课堂报告要加一个筛选
                    $conf['sceneFilter'][] = self::EXAM61_PRONUNCIATION_START;

                }
                break;
            case Assistant_Ds_WxMessageSendRecord::SEND_TYPE_DEER_CEPING_STAGE1_REPORT:
            case Assistant_Ds_WxMessageSendRecord::SEND_TYPE_DEER_CEPING_STAGE2_REPORT:
                // 获取标签列表
                $customTagList = self::getCustomTagList($courseId, $assistantUid, Service_Data_Desk_CustomTagCourseStudent::TAG_TYPE_CUSTOM_TAG);
                $operateTagList = self::getCustomTagList($courseId, $assistantUid, Service_Data_Desk_CustomTagCourseStudent::TAG_TYPE_OPERATOR_TAG);

                // 设置标签配置
                $customTag = self::CUSTOM_TAG;
                $customTagReverse = self::REVERSE_CUSTOM_TAG;
                $operateTag = self::OPERATION_TAG;
                $operateTagReverse = self::REVERSE_OPERATION_TAG;

                // 一次性设置items
                $customTag['items'] = $customTagReverse['items'] = $customTagList;
                $operateTag['items'] = $operateTagReverse['items'] = $operateTagList;

                $conf['sceneFilter'] = [self::IS_SEND, $customTag, $customTagReverse, $operateTag, $operateTagReverse];
                break;
            default :
                break;
        }


        return $conf;
    }

    public function getCourseTimeUrl()
    {
        return Api_Mercury::GetKeyForFwyy("course_time_table_url");
    }

    public function getEvaluateUrl($evaluateId, $courseId, $assistantUid)
    {
        if (empty($evaluateId) || empty($courseId) || empty($assistantUid)){
            return "";
        }

        $host = "https://support.zuoyebang.com";
        $env = Hk_Util_Env::getRunEnv();
        if ($env == Hk_Util_Env::RunEnvTest){
            $host = "https://support-base-e.suanshubang.com";
        }

        $evaluateIdStr = Hk_Util_IdCrypt::encodeUid($evaluateId);
        $sourceId = Hk_Util_IdCrypt::encodeUid($courseId);
        $dimensionKey = Hk_Util_IdCrypt::encodeUid($assistantUid);


        return sprintf("%s%s?evaluateId=%s&sourceId=%s&sourceType=%s&dimensionKey=%s&dimensionType=%s", $host, "/static/survey-questionnaire/#/home",
            $evaluateIdStr, $sourceId, Const_Common::EVALUATE_SOURCE_TYPE_COURSE, $dimensionKey, Const_Common::EVALUATE_DIMENSION_TYPE_FD_TEACHER);
    }

    public function checkSendCard($courseInfo,$lessonInfo){
        $res = false;
        //屏蔽AI课
        if(in_array($lessonInfo['playType'],[3,7])){
            return $res;
        }
        //屏蔽伴学
        if(time() > ($lessonInfo['stopTime'] + 30*60)){
            return $res;
        }
        //查询配置
        $configList = (new Service_Page_Desk_Config_KeyStrategyService())->matchStrategyList(Service_Data_Config_KeyConst::SWITCH_COURSE_APP_KEY);
        foreach ($configList as $appId => $config){
            $match1 = in_array($courseInfo['source'],$config['source']);
            $match2 = in_array($courseInfo['mainGradeId'],$config['grade']);
            $match3 = in_array($courseInfo['mainSubjectId'],$config['subject']);
            $match4 = in_array($courseInfo['newCourseType'],$config['newCourseType']);
            $match5 = in_array($courseInfo['courseId'],$config['courseIds']);
            if(($match1 && $match2 && $match3 && $match4) || $match5){
                $res = true;
            }
        }
        return $res;
    }
}